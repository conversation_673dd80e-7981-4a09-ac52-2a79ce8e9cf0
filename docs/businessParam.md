# 业务参数管理功能说明

## 功能概述

业务参数管理功能包含两个主要模块：
1. **业务参数元数据管理** - 管理参数的元数据信息
2. **业务参数值管理** - 管理参数的具体值

## 文件结构

```
src/
├── api/system/businessParam.js          # API接口定义
├── views/system/businessParam.vue       # 主页面组件
└── mock/businessParam.js               # Mock数据
```

## 功能特性

### 业务参数元数据管理
- **左树右列表布局**：左侧显示业务参数分类树，右侧显示对应分类的参数列表
- 业务分类：追缴业务、停车业务、支付业务、系统业务
- 参数级别：平台级、租户级
- 参数类型：字符串、数字、布尔值、日期、JSON
- 验证规则：支持JSON格式的验证规则
- 版本管理：支持参数版本控制
- 分类筛选：点击左侧分类树可筛选对应分类的参数

### 业务参数值管理
- 参数值设置：支持不同数据类型的参数值
- 租户隔离：支持租户级别的参数值
- 应用隔离：支持应用级别的参数值
- 备注说明：支持参数值的备注信息

## 使用方法

### 1. 启用Mock数据（开发环境）
在 `src/mock/index.js` 中修改：
```javascript
const options = {mock: true}; // 改为true启用mock
```

### 2. 访问页面
页面路径：`/system/businessParam`

### 3. 添加菜单权限
需要在后端菜单管理中添加对应的菜单项和权限：
- `business_param_add` - 新增权限
- `business_param_view` - 查看权限
- `business_param_edit` - 编辑权限
- `business_param_delete` - 删除权限

## API接口

### 元数据管理接口
- `GET /api/blade-system/business-param/metadata/list` - 获取元数据列表
- `POST /api/blade-system/business-param/metadata/submit` - 新增元数据
- `POST /api/blade-system/business-param/metadata/update` - 更新元数据
- `POST /api/blade-system/business-param/metadata/remove` - 删除元数据

### 参数值管理接口
- `GET /api/blade-system/business-param/value/list` - 获取参数值列表
- `POST /api/blade-system/business-param/value/submit` - 新增参数值
- `POST /api/blade-system/business-param/value/update` - 更新参数值
- `POST /api/blade-system/business-param/value/remove` - 删除参数值

## 数据字段说明

### 元数据字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| businessCategory | String | 业务分类 | COLLECTION_BUSINESS |
| paramLevel | String | 参数级别 | 1,2 |
| paramKey | String | 参数键（全局唯一） | collection.timeout |
| paramName | String | 参数名称 | 追缴超时时间 |
| paramType | String | 参数类型 | NUMBER |
| defaultValue | String | 默认值 | 30 |
| validationRule | String | 验证规则（JSON格式） | {"min":1,"max":365} |
| description | String | 参数描述 | 追缴业务超时时间（天） |
| isRequired | String | 是否必填 | 1,0 |
| displayOrder | Number | 显示顺序 | 1 |
| version | Number | 版本号 | 1 |

### 参数值字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| paramKey | String | 参数键 | collection.timeout |
| paramName | String | 参数名称 | 追缴超时时间 |
| paramValue | String | 参数值 | 30 |
| tenantId | String | 租户ID | tenant001 |
| appId | String | 应用ID | app001 |
| remark | String | 备注 | 租户特定配置 |

## 注意事项

1. 参数键（paramKey）需要全局唯一
2. 验证规则需要符合JSON格式
3. 参数值需要根据参数类型进行相应的格式验证
4. 删除操作会同时删除相关的参数值
5. 版本号用于参数升级时的兼容性处理 