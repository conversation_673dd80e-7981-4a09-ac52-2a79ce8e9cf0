import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/park/invoice/record/statement',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (recordId) => {
  return request({
    url: '/api/lecent-park/park/invoice/record/detail',
    method: 'get',
    params: {
      recordId
    }
  })
}

export const anewInvoice = (data) => {
  return request({
    url: '/api/lecent-park/park/invoice/customer/fail/anew/invoice',
    method: 'post',
    data: data
  })
}
// 