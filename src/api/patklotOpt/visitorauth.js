import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/visitorauth/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/visitorauth/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/visitorauth/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/visitorauth/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/visitorauth/update',
    method: 'post',
    data: row
  })
}


export const isEnter = (plate, parklotId) => {
  return request({
    url: '/api/lecent-park/visitorauth/isEnter',
    method: 'get',
    params: {
      plate,
      parklotId
    }
  })
}


export const setVisitorAuthLimit = (parklotlimit, userlimit, parklotId) => {
  console.log(parklotlimit, userlimit, parklotId)
  return request({
    url: '/api/lecent-park/visitorauth/setVisitorAuthLimit',
    method: 'get',
    params: {
      parklotlimit,
      userlimit,
      parklotId
    }
  })
}

// 获取扫码链接,用于生成二维码
export const getShorturl = (tenantId, url) => {
  return request({
    url: '/api/lecent-payment/wxAuth/getShorturl',
    method: 'post',
    data: {
      tenantId,
      url
    }
  })
}

