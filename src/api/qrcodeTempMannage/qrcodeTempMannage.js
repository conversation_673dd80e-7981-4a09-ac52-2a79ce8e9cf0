import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-report/qr/setting/list', method: 'get', params: {
      ...params, current, size,
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-report/qr/setting/remove', method: 'post', params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-report/qr/setting/save', method: 'post', data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-report/qr/setting/save', method: 'post', data: row
  })
}


export const getTempList = (params) => {
  return request({
    url: '/api/blade-report/qr/setting/template/list', method: 'get', params: {
      ...params
    }
  })
}


export const getTempDetail = (params) => {
  return request({
    url: '/api/blade-report/qr/setting/detail', method: 'get', params: {
      ...params
    }
  })
}





