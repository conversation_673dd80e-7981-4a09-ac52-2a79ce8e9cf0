import request from "@/router/axios";

// 收入分析统计
export const apiWelParkIncome = (params) => {
    return request({
        url: "/api/lecent-park/homePageStatistics/incomeHistogramStatistics",
        method: "get",
        params:{
            ...params
        }
    });
};


// 消费方式统计
export const apiWelPayWay = (params) => {
    return request({
        url: "/api/lecent-park/homePageStatistics/payWayStatistics",
        method: "get",
        params:{
            ...params
        }
    });
};

// 月卡过期预警
export const apiWelCardWarning = (params) => {
    return request({
        url: "/api/lecent-park/homePageStatistics/cardEarlyWarning",
        method: "get",
        params:{
            ...params
        }
    });
};

//  车流量统计
export const apiWelFlowRate = (params) => {
    return request({
        url: "/api/lecent-park/homePageStatistics/carFlowRateStatistics",
        method: "get",
        params:{
            ...params
        }
    });
};

//  出入场记录
export const apiWelCarEnterAndExit = (params) => {
    return request({
        url: "/api/lecent-park/homePageStatistics/enterAndExitRecord",
        method: "get",
        params:{
            ...params
        }
    });
};


//  出入场记录
export const apiGetMoney = (params) => {
    return request({
        url: "/api/lecent-park/homePageStatistics/income/today",
        method: "get",
        params:{
            ...params
        }
    });
};