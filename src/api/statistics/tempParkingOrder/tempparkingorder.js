import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/statisticsOrderPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getCarStatisticsList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/parkmerchant/merchantReport',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getStatisticsList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/statisticsOrderPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const statisticsCarDetail = (params) => {
  return request({
    url: '/api/lecent-park/parkmerchant/merchantReportStatistics',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const statisticsOrderDetail = (params) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/statisticsOrderDetail',
    method: 'get',
    params: {
      ...params,
    }
  })
}



export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/submit',
    method: 'post',
    data: row
  })
}

