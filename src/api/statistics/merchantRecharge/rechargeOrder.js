
import request from '@/router/axios';


// 提前缴费订单报表(表格数据)
export const rechargeOrderStatistics = (params) => {
    return request({
      url: '/api/lecent-park/parkmerchantruleorder/order/statistics',
      method: 'get',
      params:params
    })
}

// 提前缴费订单报表(表格数据)
export const getList = (current, size, params) => {
    return request({
      url: '/api/lecent-park/parkmerchantruleorder/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
}

