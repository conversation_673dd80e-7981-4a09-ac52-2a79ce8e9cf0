import request from '@/router/axios';


// 提前缴费订单报表(表格数据)
export const apiAdvancePayOrder = (current, size, params) => {
    return request({
      url: '/api/lecent-park/tempparkingorder/earlier-charge-statistics-page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
}


// 提前缴费订单统计
//
export const apiAdvancePayDetail = (params) => {
    return request({
      url: '/api/lecent-park/tempparkingorder/earlier-charge-statistics-detail',
      method: 'get',
      params: {
        ...params,
      }
    })
  }

//   提前缴费 超时订单详情
export const apitimeOutOrderDetail = (params) => {
  return request({
    url: '/api/lecent-park/tempparkingorder/earlier-charge-timeout-detail',
    method: 'get',
    params: {
      ...params,
    }
  })
}
