import request from '@/router/axios';

export const getList = (current, size, params) => {
	return request({
		url: '/api/blade-report/operation/statistics/list',
		method: 'get',
		params: {
			...params,
			current,
			size,
		},
	});
};

export const getChartData = params => {
	return request({
		url: '/api/blade-report/operation/statistics/trendMap/analysis',
		method: 'get',
		params: {
			...params,
		},
	});
};
export const getParkLotUser = params => {
	return request({
		url: '/api/lecent-park/userchannel/getParkLotUser',
		method: 'get',
	});
};

// export const remove = (ids) => {
//   return request({
//     url: '/api/lecent-park/ad/manage/remove',
//     method: 'post',
//     params: {
//       ids,
//     }
//   })
// }

// export const add = (row) => {
//   return request({
//     url: '/api/lecent-park/ad/manage/save',
//     method: 'post',
//     data: row
//   })
// }

// export const update = (row) => {
//   return request({
//     url: '/api/lecent-park/ad/manage/update',
//     method: 'post',
//     data: row
//   })
// }
