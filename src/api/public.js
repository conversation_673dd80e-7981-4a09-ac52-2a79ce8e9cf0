import request from '@/router/axios';
import { getToken } from '@/util/auth'

import { uploadFileUrl } from '@/util/processUrlReplace'
/**
 *
 * @param {parklotId} 根据parklotId查询商户列表
 * @returns
 */
export const getMerchantByParklotId = (parklotId) => {
  return request({
    url: '/api/lecent-park/parkmerchant/getMerchantByParklotId',
    method: 'get',
    params: {
      parklotId
    }
  })
}


/**
 * 上传图片
 * @param file
 */
export const uploadImg = (file) => {
  let f = new FormData();
  f.append('file', file);
  return request({
    headers: {
      'content-type': `multipart/form-data`
    },
    url: '/api/blade-resource/oss/endpoint/put-file',
    method: 'post',
    data: f
  })
};



/**
 * 上传文件
 * @param file
@templateId string
@processId string
*/
export const uploadFlie = (file) => {
  let f = new FormData();
  f.append('file', file);
  return request({
    headers: {
      'content-type': `multipart/form-data`
    },
    url: '/api/blade-resource/oss/endpoint/put-file',
    method: 'post',
    data: f
  })
};


// 获取路外车场列表
export const getParkList = () => {
  return request({
    url: "/api/lecent-park/park/parklot/userParklotOutList",
    method: "get",
  });
};

// 获取路内车场列表
export const getParkInList = () => {
  return request({
    url: '/api/lecent-park/park/parklot/ParklotInList',
    method: 'get',
  })
};

// 根据车场获取对应的收费规则
export const getTempRuleListFormParklotId = (parklotId) => {
  return request({
    url: '/api/lecent-park/tempparkingchargerule/temp/rule',
    method: 'get',
    params: {
      parklotId
    }
  })
};

// 根据车场获取对应的车位
export const getParkingplaceListByPlaceCode = (parklotIdStr, placeCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/listByPlaceCode?',
    method: 'get',
    params: {
      parklotIdStr,
      placeCode
    }
  })
};



//导出数据到excel  正常下载，通过在请求参数中添加lecent-Auth代替header
export const apiDownloadQrParklot = (data, url, method = 'post') => {
  console.log(data)
  let lecentAuth = getToken()
  url = uploadFileUrl(url) + '?Blade-Auth=' + lecentAuth
  // 创建form元素
  var temp_form = document.createElement("form");
  // 设置form属性
  temp_form.action = url;
  temp_form.target = "_blank";
  temp_form.method = method;
  temp_form.style.display = "none";
  // 处理需要传递的参数
  for (var x in data) {
    var opt = document.createElement("textarea");
    opt.name = x;
    opt.value = data[x];
    temp_form.appendChild(opt);
  }
  document.body.appendChild(temp_form);
  // 提交表单      u
  temp_form.submit();


}






// 根据车场获取对应的收费规则
export const apiDistrict = (params) => {
  return request({
    url: '/api/blade-system/district/tree',
    method: 'get',
    params: {
      ...params
    }
  })
};

// 获取对应的字典

export const getSystemDict = (key) => {
  return request({
    url: '/api/blade-system/dict/dictionary?code=' + key,
    method: 'get'
  })
}


//获取车位
export const getParkingPlace = (parklotIdStr, placeCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/list',
    method: 'get',
    params: {
      parklotIdStr,
      placeCode
    }
  })
}

//获取分组车位
export const getParkingGroupingPlace = (parklotIdStr, placeCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/select-list',
    method: 'get',
    params: {
      parklotIdStr,
      placeCode
    }
  })
}


//秒图获取
export const getTimeImage = (current, size, parkingPlaceId, startTime, endTime, ascsStr) => {
  let data = {
    current: current,
    size: size,
    captureTimeStart: startTime,
    captureTimeEnd: endTime,
  }
  data[ascsStr] = 'capture_time'
  return request({
    url: `/api/lecent-park/parking-place/${parkingPlaceId}/camera-capture-records/page`,
    method: 'get',
    params: data
  });
};
//获取车位
export const getPlaceEventLog = (parkingPlaceId, current, size, startDate, endDate, ascsStr) => {
  let paramsType = {
    current: current,
    size: size,
    startTime: startDate,
    endTime: endDate,
  }
  paramsType[ascsStr] = 'event_time'
  return request({
    url: `/api/lecent-park/parking-place/${parkingPlaceId}/event-log/page`,
    method: 'get',
    params: paramsType
  })
}


//导入公共接口
export const importExport = (data) => {
  return request({
    url: '/api/lecent-park/importExport/import',
    method: 'post',
    data
  })
}

//导处公共接口
export const requestExport = (serviceId,businessType,params) => {
  let data = {
    businessType,
    'params': JSON.stringify(params)
  }
  return request({
    url: '/api/'+serviceId+'/importExport/export',
    method: 'post',
    data
  })
}
// 导入记录公共接口
export const importExportRecordPage = (serviceId,current, size, params) => {
  return request({
    url: '/api/' + serviceId + '/importExport/record/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  })
}
// 导入记录详情
export const importExportRecordInfoPage = (serviceId,current, size, params) => {
  return request({
    url: '/api/' + serviceId + '/importExport/record/item/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  })
}

// 获取工单详情
export const getWorkOrderDetail = (id) => {
  return request({
      url: "/api/lecent-csc/work-order/detail",
      method: "get",
      params:{
          id:id,
      }
  });
}


// /lecent-park-zxr/parkingplace/place/iterator/{placeId}
export const getParkingPlaceIterator = (placeId) => {
  return request({
    url: `/api/lecent-park/parkingplace/place/iterator/${placeId}`,
    method: 'get',
  })
}

//根据parklotId获取设备统计
export const getDeviceStatistics = (parklotId) => {
  return request({
    url: `/api/lecent-park/parklotDeviceRet/countDeviceTypeByParklotId?parklotId=${parklotId}`,
    method: 'get',
  })
}

//查询所有商户
export const getMerchantList = () => {
  return request({
    url: '/api/blade-report/report/collection/merchant-list',
    method: 'get',
  })
}