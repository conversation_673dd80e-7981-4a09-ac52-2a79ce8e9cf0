import request from '@/router/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/lecent-park/merge-order/recover-order/page',
        method: 'get',
        params: {
            ...params, current, size,
        }
    })
}


export const count = (params) => {
    return request({
        url: '/api/lecent-park/merge-order/recover-order/count',
        method: 'get',
        params: {
            ...params
        }
    })
}
