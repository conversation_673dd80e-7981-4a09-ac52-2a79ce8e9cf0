import request from "@/router/axios";

// 获取排班列表
export const getList = (parkLotIdStr, startTime, endTime) => {
  return request({
    url: "/api/lecent-park/schedules/list",
    method: "get",
    params: {
      parkLotIdStr,
      startTime,
      endTime
    }
  });
};

// 提交排班
export const submit = (data) => {
  return request({
    url: "/api/lecent-park/schedules/submit",
    method: "post",
    data
  });
};

export const schedulesShiftPage = ( current,size,) => {
  return request({
    url: "/api/lecent-park/schedules/shift/page",
    method: "get",
    params: {
      current,
      size,
    }
  });
};

export const schedulesShiftSubmit = ( data) => {
  return request({
    url: "/api/lecent-park/schedules/shift/submit",
    method: "post",
    data
  });
};

export const schedulesShiftRemove = ( ids) => {
  return request({
    url: "/api/lecent-park/schedules/shift/remove?ids="+ids,
    method: "post",
  });
};

export const dictionary = ( ) => {
  return request({
    url: "/api/blade-system/dict/dictionary?code=schedules_role",
    method: "get",
  });
};
export const listByRole = (roleAlias) => {
  return request({
    url: "/api/blade-user/list-by-role?roleAlias="+roleAlias,
    method: "get",
  });
};

export const remove = (data) => {
  return request({
    url: "/api/lecent-park/schedules/clean",
    method: "post",
    data:data
  });
};
