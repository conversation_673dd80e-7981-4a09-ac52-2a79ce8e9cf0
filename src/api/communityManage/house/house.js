import request from '@/router/axios';

const API= "/api/lecent-community";

/*
  分页记录
 */
export const getPage = (current, size, params) => {
  return request({
    url:API+'/house/page',
    method: 'get',
    params: {...params, current, size}
  })
};

export const saveUpdate = (data) => {
  return request({
    url: API+'/house/submit',
    method: 'post',
    data: data
  })
};

export const remove = (ids) => {
  return request({
    url: API+'/house/remove',
    method: 'post',
    params: {ids}
  })
};


export const houseList= (params) => {
  return request({
    url: API+'/house/list',
    method: 'get',
    params: {...params}
  })
};








