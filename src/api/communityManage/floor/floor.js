import request from '@/router/axios';

const API= "/api/lecent-community";

/*
  分页记录
 */
export const getPage = (current, size, params) => {
  return request({
    url:API+'/floor/page',
    method: 'get',
    params: {...params, current, size}
  })
};

export const saveUpdate = (data) => {
  return request({
    url: API+'/floor/submit',
    method: 'post',
    data: data
  })
};

export const remove = (ids) => {
  return request({
    url: API+'/floor/remove',
    method: 'post',
    params: {ids}
  })
};


export  const  floorList= (params) => {
  return request({
    url: API+'/floor/list',
    method: 'get',
    params: {...params}
  })
};

/**
 *
 * 获取房屋类型
 */
export const getTypeList= () => {
  return request({
    url: '/api/lecent-community/floor/house-type-select',
    method: 'get',
  })
};







