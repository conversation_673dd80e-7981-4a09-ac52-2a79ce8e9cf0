import request from '@/router/axios';

const API= "/api/lecent-community";

/*
  分页记录
 */
export const getPage = (current, size, params) => {
  return request({
    url:API+'/group/page',
    method: 'get',
    params: {...params, current, size}
  })
};

export const saveUpdate = (data) => {
  return request({
    url: API+'/group/submit',
    method: 'post',
    data: data
  })
};

export const remove = (ids) => {
  return request({
    url: API+'/group/remove',
    method: 'post',
    params: {ids}
  })
};


export const groupList= (params) => {
  return request({
    url: API+'/group/list',
    method: 'get',
    params: {...params}
  })
};








