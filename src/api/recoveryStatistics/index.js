import request from '@/router/axios';
export const getList = (current, size, params) => {
  return request({
    url: `/api/blade-report/report/collection/merchant`,
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

//获取统计
export const getStatistics = (params) => {
    return request({
      url: `/api/blade-report/report/collection/total`,
      method: 'get',
      params: params
    })
  }

//获取折线图数据
export const getLineChart = (params) => {
  return request({
    url: `/api/blade-report/report/collection/daily`,
    method: 'get',
    params: params
  })
}




