import request from '@/router/axios';
import { getToken } from '@/util/auth'

/**
 * 获取车场列表
 * @returns {AxiosPromise}
 */
export const getParklotList = () => {
  return request({
    url: '/api/lecent-park/park/parklot/dropDownList',
    method: 'get',
  })
};

/**
 * 获取车位列表
 * @param parklotIdStr
 * @returns {AxiosPromise}
 */
export const getParklotPlaceList = (parklotIdStr) => {
  return request({
    url: '/api/lecent-park/parkingplace/select-list',
    method: 'get',
    params: {
      parklotIdStr,
    }
  })
}

// 获取车场信息
export const getParkLotInfo = (id) => {
  return request({
    url: '/api/lecent-park/park/parklot/detail',
    method: 'get',
    params: {
      id
    }
  })
};

// 获取设备信息
export const getDeviceInfo = (id) => {
  return request({
    url: '/api/lecent-park/parkingplace/getParkSpaceStateStatistics',
    method: 'get',
    params: {
      parklotId: id,
    }
  })
};

/**
 * 获取车位信息
 * @param payCode
 * @returns {*}
 */
export const getParkingByPayCode = (payCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/page',
    method: 'get',
    params: {
      payCode,
      current: 1,
      size: 10,
    }
  })
};

/**
 * 获取停车状态
 * @param id
 * @returns {AxiosPromise}
 */
export const getParkingStatus = (id) => {
  return request({
    url: '/api/lecent-park/parkingplace/runtime-status/' + id,
    method: 'get',

  })
}


/**
 * 获取对应的字典
 * @param key
 * @returns {AxiosPromise}
 */
export const getDict = (key) => {
  return request({
    url: '/api/blade-system/dict/dictionary?code=' + key,
    method: 'get'
  })
}

/**
 * 获取秒图
 * @param current
 * @param size
 * @param parkingPlaceId
 * @param startTime
 * @param endTime
 * @returns {AxiosPromise}
 */
export const getTimeImage = (current,size,parkingPlaceId, startTime, endTime) => {
  return request({
    url: `/api/lecent-park/parking-place/${parkingPlaceId}/camera-capture-records/page`,
    method: 'get',
    params: {
      current: current,
      size: size,
      ascs: 'capture_time',
      captureTimeStart:startTime,
      captureTimeEnd:endTime,
    }
  });
};

export const getPlaceEventLog = (parkingPlaceId, startDate, endDate, current, size) => {
  return request({
    url: `/api/lecent-park/parking-place/${parkingPlaceId}/event-log/page`,
    method: 'get',
    params: {
      current: current,
      ascs: 'event_time',
      size: size,
      startTime: startDate,
      endTime: endDate,
    }
  })
}









// ------------------------------------

import { uploadFileUrl } from '@/util/processUrlReplace'
/**
 *
 * @param {parklotId} 根据parklotId查询商户列表
 * @returns
 */
export const getMerchantByParklotId = (parklotId) => {
  return request({
    url: '/api/lecent-park/parkmerchant/getMerchantByParklotId',
    method: 'get',
    params: {
      parklotId
    }
  })
}


/**
 * 上传图片
 * @param file
 */
export const uploadImg = (file) => {
  let f = new FormData();
  f.append('file', file);
  return request({
    headers: {
      'content-type': `multipart/form-data`
    },
    url: '/api/blade-resource/oss/endpoint/put-file',
    method: 'post',
    data: f
  })
};



/**
 * 上传文件
 * @param file
@templateId string
@processId string
*/
export const uploadFlie = (file) => {
  let f = new FormData();
  f.append('file', file);
  return request({
    headers: {
      'content-type': `multipart/form-data`
    },
    url: '/api/blade-resource/oss/endpoint/put-file',
    method: 'post',
    data: f
  })
};


// 获取路外车场列表
export const getParkList = () => {
  return request({
    url: "/api/lecent-park/park/parklot/userParklotOutList",
    method: "get",
  });
};

// 获取路内车场列表
export const getParkInList = () => {
  return request({
    url: '/api/lecent-park/park/parklot/ParklotInList',
    method: 'get',
  })
};

// 根据车场获取对应的收费规则
export const getTempRuleListFormParklotId = (parklotId) => {
  return request({
    url: '/api/lecent-park/tempparkingchargerule/temp/rule',
    method: 'get',
    params: {
      parklotId
    }
  })
};

// 根据车场获取对应的车位
export const getParkingplaceListByPlaceCode = (parklotIdStr, placeCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/listByPlaceCode?',
    method: 'get',
    params: {
      parklotIdStr,
      placeCode
    }
  })
};



//导出数据到excel  正常下载，通过在请求参数中添加lecent-Auth代替header
export const apiDownloadQrParklot = (data, url, method = 'post') => {
  console.log(data)
  let lecentAuth = getToken()
  url = uploadFileUrl(url) + '?Blade-Auth=' + lecentAuth
  // 创建form元素
  var temp_form = document.createElement("form");
  // 设置form属性
  temp_form.action = url;
  temp_form.target = "_blank";
  temp_form.method = method;
  temp_form.style.display = "none";
  // 处理需要传递的参数
  for (var x in data) {
    var opt = document.createElement("textarea");
    opt.name = x;
    opt.value = data[x];
    temp_form.appendChild(opt);
  }
  document.body.appendChild(temp_form);
  // 提交表单      u
  temp_form.submit();


}






// 根据车场获取对应的收费规则
export const apiDistrict = (params) => {
  return request({
    url: '/api/blade-system/district/tree',
    method: 'get',
    params: {
      ...params
    }
  })
};

// 获取对应的字典

export const getSystemDict = (key) => {
  return request({
    url: '/api/blade-system/dict/dictionary?code=' + key,
    method: 'get'
  })
}


//获取车位
export const getParkingPlace = (parklotIdStr, placeCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/list',
    method: 'get',
    params: {
      parklotIdStr,
      placeCode
    }
  })
}

//获取分组车位
export const getParkingGroupingPlace = (parklotIdStr, placeCode) => {
  return request({
    url: '/api/lecent-park/parkingplace/select-list',
    method: 'get',
    params: {
      parklotIdStr,
      placeCode
    }
  })
}

