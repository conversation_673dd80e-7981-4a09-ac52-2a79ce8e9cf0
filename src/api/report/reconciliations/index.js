import request from '@/router/axios';

export const getAccountsReconciliationList = (current, size, params) => {
    return request({
        url: '/api/leliven-finance/bill/detail/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const dictionary = (params) => {
    return request({
        url: '/api/blade-system/dict/dictionary',
        method: 'get',
        params: {
            ...params,
        }
    })
}

export const getList = (current, size, params) => {
    return request({
        url: '/api/leliven-finance/bill/total/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getTotalDetail = (params) => {
    return request({
        url: '/api/leliven-finance/bill/total/detail',
        method: 'get',
        params: {
            ...params
        }
    })
}

export const getDetailHandler = (params) => {
    return request({
        url: '/api/leliven-finance/bill/detail/handler',
        method: 'post',
        data: params
    })
}

export const recreate = (params) => {
    return request({
        url: '/api/leliven-finance/bill/total/recreate',
        method: 'post',
        data: params
    })
}



export const accountPage = (current, size, params) => {
    return request({
        url: '/api/leliven-finance/payee/account/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const accountSave = (params) => {
    return request({
        url: '/api/leliven-finance/payee/account/submit',
        method: 'post',
        data: params
    })
}




export const accountRemove = (params) => {
    return request({
        url: '/api/leliven-finance/payee/account/remove',
        method: 'post',
        params: {
            ...params
        }
    })
}

export const settleSave = (params) => {
    return request({
        url: '/api/leliven-finance/bill/settle/save',
        method: 'post',
        data: params
    })
}


export const settlePage = (current, size, params) => {
    return request({
        url: '/api/leliven-finance/bill/settle/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const settleDetail = (params) => {
    return request({
        url: '/api/leliven-finance/bill/settle/detail',
        method: 'get',
        params: {
            ...params,
        }
    })
}

export const orderSave = (params) => {
    return request({
        url: '/api/lecent-park/temp/parking/refund/order/save',
        method: 'post',
        data: params
    })
}

export const uploadProof = (params) => {
    return request({
        url: '/api/leliven-finance/bill/splitting/total/upload/proof',
        method: 'post',
        data: params
    })
}

export const settleCancel = (params) => {
    return request({
        url: '/api/leliven-finance/bill/settle/cancel',
        method: 'post',
        data: params
    })
}

export const UploadExcel = file => {
    let f = new FormData();
    f.append('file', file);
    return request({
        url: '/api/leliven-finance/import/batch/detail',
        headers: {
            'content-type': 'multipart/form-data',
        },
        method: 'POST',
        data: f,
    });
};