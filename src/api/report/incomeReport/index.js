import request from '@/router/axios';

export const earningsCharte = (params) => {
    return request({
        url: '/api/blade-report/statistical/statement/earnings/chart',
        method: 'get',
        params: {
            ...params,
        }
    })
}

export const earningsTable = (params) => {
    return request({
        url: '/api/blade-report/statistical/statement/earnings/table',
        method: 'get',
        params: {
            ...params,
        }
    })
}

export const parklotIdSpace = (parklotId) => {
    return request({
        url: '/api/lecent-park/parkingplace/list/parklotId',
        method: 'get',
        params: { parklotId }
    })
}