import request from '@/router/axios';
import { getToken } from '@/util/auth'
import { uploadFileUrl } from '@/util/processUrlReplace'

export const gettableList = (params) => {
    return request({
        url: '/api/blade-report/statistical/statement/finance/list',
        method: 'get',
        params: {
            ...params,
        }
    })
}

export const gettabNewleList = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/findFinanceStatistics',
    method: 'get',
    params: {
      ...params,
    }
  })
}



export const exportData2excel = (data, url, method = "post") => {
    let lecentAuth = getToken()
    url = uploadFileUrl(url) + '?Blade-Auth=' + lecentAuth

    // 创建form元素
    var temp_form = document.createElement("form");
    // 设置form属性
    temp_form.action = url;
    temp_form.target = "_blank";
    temp_form.method = method;
    temp_form.style.display = "none";
    // 处理需要传递的参数
    for (var x in data) {
        var opt = document.createElement("textarea");
        opt.name = x;
        opt.value = data[x];
        temp_form.appendChild(opt);
    }
    //创建token验证参数
    // let lecentAuth = getToken();
    let jwt = document.createElement("textarea");
    jwt.name = "Blade-Auth";
    jwt.value = lecentAuth;
    temp_form.appendChild(jwt);

    document.body.appendChild(temp_form);
    console.log(temp_form)
    // 提交表单
    temp_form.submit();
};

export const dictionary = () => {
  return request({
    url: '/api/blade-system/dict/dictionary?code=pay_channel',
    method: 'get',
  })
}
