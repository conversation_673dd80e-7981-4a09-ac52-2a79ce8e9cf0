import request from '@/router/axios';


export const exportRecordList = (params) => {
  return request({
    url: '/api/blade-report/exportRecord/list',
    method: 'get',
    params: {
      ...params,
    }
  })
}


export const exportParkingOrder = (row) => {
  return request({
    url: '/api/blade-report/exportRecord/parkingOrder-export',
    method: 'post',
    data: row
  })
}

export const exportChannelTodo = (row) => {
  return request({
    url: '/api/blade-report/exportRecord/channelTodo-export',
    method: 'post',
    data: row
  })
}

export const exportTempOrder = (row) => {
  return request({
    url: '/api/blade-report/exportRecord/tempOrder-export',
    method: 'post',
    data: row
  })
}

export const exportCardOrder = (row) => {
  return request({
    url: '/api/blade-report/report/card/excelOrder',
    method: 'get',
    params: row
  })
}

/***
 * 停车报表统计数据
 * */


export const getstatement = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/home',
    method: 'get',
    params: {
      ...params,
    }
  })
}
/***
 * 停车报表表格数据
 * */
export const gettableList = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/page',
    method: 'get',
    params: {
      ...params,
    }
  })
}

/***
 * 停车报表平均停车率 返回白天、夜晚、平均
 * */
export const getParkingRatio = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/earnings/moring/parking/ratio',
    method: 'get',
    params: {
      ...params,
    }
  })
}

/***
 * 停车报表平均停车率 合计
 * */
export const getParkingRatioTotal = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/total/parking-ratio',
    method: 'get',
    params: {
      ...params,
    }
  })
}

/***报表详情*/
export const getParklotDetail = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/parklot/detail',
    method: 'get',
    params: {
      ...params,
    }
  })
}


/***报表表格列表*/
export const getTableDetail = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/order/page',
    method: 'get',
    params: {
      ...params,
    }
  })
}
/***报表表格列表*/
export const getTotalDetail = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/total',
    method: 'get',
    params: {
      ...params,
    }
  })
}

/** 获取分时停车率 */
export const parkingRatioe = (params) => {
  return request({
    url: '/api/blade-report/statistical/statement/earnings/chart/parking/ratio',
    method: 'get',
    params: {
      ...params,
    }
  })
}



