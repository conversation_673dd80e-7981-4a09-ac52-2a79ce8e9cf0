import request from '@/router/axios';

let addReportPath = process.env.VUE_APP_REPORT_PATH || "/"
export const getList = (current, size, params) => {
  return request({
    url: `/api/blade-report${addReportPath}report/list`,
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const remove = (ids) => {
  return request({
    url: `/api/blade-report${addReportPath}report/remove`,
    method: 'post',
    params: {
      ids,
    }
  })
}
export const update = (row) => {
  return request({
    url: `/api/blade-report${addReportPath}report/update`,
    method: 'post',
    data:row
  })
}


export const sheetBaseConfig = (params)=>{
  // let addReportPath = process.env.VUE_APP_REPORT_PATH || "/"
  return request({
    url: `/api/blade-report${addReportPath}report/get/setting`,
    method: 'get',
    params:{
      ...params
    }
  })
}









