import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-system/district/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-system/district/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-system/district/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-system/district/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-system/district/submit',
    method: 'post',
    data: row
  })
}

export const getLazyList = (parentId, params) => {
  return request({
    url: '/api/blade-system/district/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
}

export const getTree = () => {
  return request({
    url: '/api/blade-system/district/tree',
    method: 'get',
  })
};

export const getListByParentId = (parentCode) => {
  return request({
    url: '/api/blade-system/district/getNextLevelDistricts',
    method: 'get',
    params: {
      parentCode
    }
  })
};
