import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-system/app-api/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-system/app-api/detail',
    method: 'get',
    params: {
      id
    }
  })
}


export const getApiOptions = () => {
  return request({
    url: '/api/blade-system/app-api/options',
    method: 'get',
  })
}


export const remove = (ids) => {
  return request({
    url: '/api/blade-system/app-api/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-system/app-api/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-system/app-api/submit',
    method: 'post',
    data: row
  })
}

