import request from '@/router/axios';

// 业务参数元数据管理
export const getMetadataList = (current, size, params) => {
  return request({
    url: '/api/blade-system/business-param/metas/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const removeMetadata = (ids) => {
  return request({
    url: '/api/blade-system/business-param/metadata/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const submitMetadata = (row) => {
  return request({
    url: '/api/blade-system/business-param/metadata/submit',
    method: 'post',
    data: row
  })
}

export const updateMetadata = (row) => {
  return request({
    url: '/api/blade-system/business-param/metadata/update',
    method: 'post',
    data: row
  })
}

// 业务参数值管理
export const getValueList = (current, size, params) => {
  return request({
    url: '/api/blade-system/business-param/value/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const removeValue = (ids) => {
  return request({
    url: '/api/blade-system/business-param/value/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const submitValue = (row) => {
  return request({
    url: '/api/blade-system/business-param/value/submit',
    method: 'post',
    data: row
  })
}

export const updateValue = (row) => {
  return request({
    url: '/api/blade-system/business-param/value/update',
    method: 'post',
    data: row
  })
} 