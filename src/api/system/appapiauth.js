import request from '@/router/axios';

export const getAuthPage = (params) => {
  return request({
    url: '/api/blade-system/app-api-auth/list',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-system/app-api-auth/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-system/app-api-auth/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-system/app-api-auth/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-system/app-api-auth/submit',
    method: 'post',
    data: row
  })
}

export const saveAuth = (row) => {
  return request({
    url: '/api/blade-system/app-api-auth/submit',
    method: 'post',
    data: row
  })
}


export const update = (row) => {
  return request({
    url: '/api/blade-system/app-api-auth/submit',
    method: 'post',
    data: row
  })
}

