import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/receiptprintconfig/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}



export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/receiptprintconfig/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/receiptprintconfig/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/receiptprintconfig/update',
    method: 'post',
    data: row
  })
}

export const simpleList = () => {
  return request({
    url: '/api/lecent-park/receiptprintconfig/simpleList',
    method: 'get'
  })
}

