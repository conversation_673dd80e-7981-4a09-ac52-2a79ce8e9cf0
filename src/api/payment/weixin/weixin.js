import request from '@/router/axios';
import crypto from "@/util/crypto";

export const getList = (current, size, params) => {
  const param = {
    ...params,
    current,
    size,
  }
  const data = crypto.encryptAES(JSON.stringify(param), crypto.aesKey);
  return request({
    url: '/api/lecent-payment/wx/config/page',
    method: 'get',
    params: {data}
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-payment/wx/config/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-payment/wx/config/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-payment/wx/config/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-payment/wx/config/submit',
    method: 'post',
    data: row
  })
}

