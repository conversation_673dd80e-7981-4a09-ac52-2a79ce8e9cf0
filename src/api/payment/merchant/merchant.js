import request from '@/router/axios';
import crypto from "@/util/crypto";

export const getList = (current, size, params) => {
  const param = {
    ...params,
    current,
    size,
  }
  const data = crypto.encryptAES(JSON.stringify(param), crypto.aesKey);
  return request({
    url: '/api/lecent-payment/merchant/page',
    method: 'get',
    params: {
      data
    }
  })
}

export const getDetail = (id) => {
  const param = {
    id
  }
  const data = crypto.encryptAES(JSON.stringify(param), crypto.aesKey);
  return request({
    url: '/api/lecent-payment/merchant/detail',
    method: 'get',
    params: {
      data
    }
  })
}

export const remove = (ids) => {
  const data = crypto.encryptAES(ids, crypto.aesKey);
  return request({
    url: '/api/lecent-payment/merchant/remove',
    method: 'post',
    params: {
      data,
    }
  })
}

export const add = (row) => {
  return request({
    headers: {
      'Content-Type': 'application/json'
    },
    url: '/api/lecent-payment/merchant/submit',
    method: 'post',
    text: true,
    data: crypto.encryptAES(JSON.stringify(row), crypto.aesKey)
  })
}


export const update = (row) => {
  return request({
    headers: {
      'Content-Type': 'application/json'
    },
    url: '/api/lecent-payment/merchant/submit',
    method: 'post',
    text: true,
    data: crypto.encryptAES(JSON.stringify(row), crypto.aesKey)
  })
}

