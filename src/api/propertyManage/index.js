import request from '@/router/axios';


export const getList = (current, size, params) => {
    return request({
        url: '/api/lecent-park/ownerdetailinfo/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getDetail = (id) => {
    return request({
        url: '/api/lecent-park/ownerdetailinfo/detail',
        method: 'get',
        params: {
            id
        }
    })
}

export const communityTree = (parentId, ids) => {
    return request({
        url: '/api/lecent-park/community/tree', method: 'get', params: {
            parentId, ids
        }
    })
}

export const remove = (ids) => {
    return request({
        url: '/api/lecent-park/ownerdetailinfo/delete',
        method: 'post',
        params: {
            ids,
        }
    })
}

export const submit = (data) => {
    return request({
        url: '/api/lecent-park/ownerdetailinfo/submit',
        method: 'post',
        data: data
    })
}