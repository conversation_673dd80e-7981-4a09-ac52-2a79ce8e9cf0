import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/freecardauth/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/freecardauth/detail',
    method: 'get',
    params: {
      id
    }
  })
}
export const freeCardByIdAndParkLotId = (params) => {
  return request({
    url: '/api/lecent-park/freecardauth/freeCardByIdAndParkLotId',
    method: 'get',
    params: {
      ...params,
    }
  })
};

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/freecardauth/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/freecardauth/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/freecardauth/submit',
    method: 'post',
    data: row
  })
}


export const batchDelete = (ids, parkLotId) => {
  return request({
    url: '/api/lecent-park/freecardauth/batchDelete',
    method: 'post',
    params: {
      ids, parkLotId
    }
  })
}

