import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/biz-opt-log/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/biz-opt-log/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/biz-opt-log/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/biz-opt-log/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/biz-opt-log/submit',
    method: 'post',
    data: row
  })
}


export const logExport = (row) => {
  return request({
    url: '/api/lecent-park/biz-opt-log/export',
    method: 'post',
    data: row
  })
}
