import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/freecardtype/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/freecardtype/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/freecardtype/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/freecardtype/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/freecardtype/submit',
    method: 'post',
    data: row
  })
}


export const listAll = () => {
  return request({
    url: '/api/lecent-park/freecardtype/listAll',
    method: 'get'
  })
}
