import request from '@/router/axios';

export const getPage = (current, size, params) => {
  return request({
    url: '/api/lecent-park/channel/pageByPropertyMgr',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/channel/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/channel/detail',
    method: 'get',
    params: {
      id
    }
  })
}


export const getByParkLotId = (parkLotId) => {
  return request({
    url: '/api/lecent-park/channel/getByParkLotId',
    method: 'get',
    params: {
      parkLotId
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/channel/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/channel/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/channel/update',
    method: 'post',
    data: row
  })
}


export const saveShortUrl = (row) => {
  return request({
    url: '/api/lecent-park/channel/saveShortUrl',
    method: 'post',
    data: row
  })
}


export const getChannelPage = (current, size, params) => {
  return request({
    url: '/api/lecent-park/channel/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


export const getListByParklotId = (parklotId) => {
  return request({
    url: '/api/lecent-park/client/getChannelListByParklotId',
    method: 'get',
    params: {
      parklotId
    }
  })
}


export const createQr = (id) => {
  return request({
    url: '/api/lecent-park/channel/create-qr',
    method: 'post',
    params: {
      id
    }
  })
}


