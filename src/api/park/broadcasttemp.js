import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/broadcast-temp/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/broadcast-temp/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/broadcast-temp/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/broadcast-temp/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/broadcast-temp/submit',
    method: 'post',
    data: row
  })
}



export const getAll = () => {
  return request({
    url: '/api/lecent-park/broadcast-temp/getAll',
    method: 'get',
  })
}
