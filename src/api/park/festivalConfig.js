import request from '@/router/axios';


export const getConfigInfoByParklotId = (parklotId) => {
  return request({
    url: '/api/lecent-park/parklotextentsetting/getConfigInfoByParklotId',
    method: 'get',
    params: {
      parklotId
    }
  })
}


export const submitConfig = (row) => {
  return request({
    url: '/api/lecent-park/parklotextentsetting/submitFestivalInfo',
    method: 'post',
    data: row
  })
}
