import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/freecard/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/freecard/detail',
    method: 'get',
    params: {
      id
    }
  })
}


export const batchUpdateStatus = (authIds) => {
  return request({
    url: '/api/lecent-park/freecardauth/batchUpdateStatus',
    method: 'get',
    params: {
      authIds
    }
  })
}

export const cancelAuth = (id, memo) => {
  return request({
    url: '/api/lecent-park/freecardauth/cancelAuth',
    method: 'get',
    params: {
      id, memo
    }
  })
}

export const recoverAuth = (id, memo) => {
  return request({
    url: '/api/lecent-park/freecardauth/recoverAuth',
    method: 'get',
    params: {
      id, memo
    }
  })
}


export const getListByFreeCardId = (freeCardId) => {
  return request({
    url: '/api/lecent-park/freecardauth/getListByFreeCardId',
    method: 'get',
    params: {
      freeCardId
    }
  })
}


export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/freecard/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/lecent-park/freecard/save',
    method: 'post',
    data: row
  })
}


export const addAuth = (row) => {
  return request({
    url: '/api/lecent-park/freecard/saveAuth',
    method: 'post',
    data: row
  })
}

export const updateAuth = (row) => {
  return request({
    url: '/api/lecent-park/freecard/updateAuth',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/freecard/submit',
    method: 'post',
    data: row
  })
}

/**
 * 修改免费卡
 * @param row
 */
export const updateFreeCard = (row) => {
  return request({
    url: '/api/lecent-park/freecard/updateFreeCard',
    method: 'post',
    data: row
  })
}


/**
 * 延长授权时间
 * @param row
 */
export const extendEndTime = (row) => {
  return request({
    url: '/api/lecent-park/freecardauth/extendEndTime',
    method: 'post',
    data: row
  })
}


export const logoutCard = (cardId) => {
  return request({
    url: '/api/lecent-park/freecard/logoutCard',
    method: 'get',
    params: {
      cardId
    }
  })
}

export const batchCancelAuth = (authIds) => {
  return request({
    url: '/api/lecent-park/freecardauth/batchCancelAuth',
    method: 'get',
    params: {
      authIds
    }
  })
}

export const batchRecoverAuth = (authIds) => {
  return request({
    url: '/api/lecent-park/freecardauth/batchRecoverAuth',
    method: 'get',
    params: {
      authIds
    }
  })
}


// 删除授权
export const batchRemoveAuth = (data) => {
  return request({
    url: '/api/lecent-park/freecardauth/remove',
    method: 'post',
    params: {
      ...data
    }
  })
}

// 批量授权
export const batchExtendEndTime = (data) => {
  return request({
    url: '/api/lecent-park/freecardauth/batchExtendEndTime',
    method: 'post',
    data: data
  })
}