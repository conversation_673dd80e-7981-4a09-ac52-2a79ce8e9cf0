import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const getDateDetail = (parklotId) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/getDateDetail',
    method: 'get',
    params: {
      parklotId
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const submit = (form) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/submit',
    method: 'post',
    data: form
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/submit',
    method: 'post',
    data: row
  })
}

export const add = (form) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/add',
    method: 'post',
    data: form
  })
}


export const addCall = (form) => {
  return request({
    url: '/api/lecent-park/dparklotsmssetting/addCall',
    method: 'post',
    data: form
  })
}

