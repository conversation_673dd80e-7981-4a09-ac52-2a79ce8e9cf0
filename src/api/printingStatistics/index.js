
import request from '@/router/axios';


export const getPrintingStatistics = (startTime, endTime, rankingType) => {
    return request({
        url: '/api/blade-report/collector/analysis/ranking',
        method: 'get',
        params: {
            startTime,
            endTime,
            rankingType
        }
    })
}
export const getPrintingStatisticsPage = (current, size, params) => {
    return request({
        url: '/api/blade-report/collector/analysis/data/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}

export const getUserList = () => {
    return request({
        url: '/api/blade-user/list-by-role?roleAlias=toll',
        method: 'get',

    })
}

//获取列表统计
export const getPrintingStatisticsTotal = (params) => {
    return request({
        url: '/api/blade-report/collector/analysis/data/total',
        method: 'get',
        params
    })
}