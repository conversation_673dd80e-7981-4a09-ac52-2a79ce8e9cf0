import request from '@/router/axios';
export const getList = (current, size, params) => {
  return request({
    url: `/api/blade-report/pay-channel-statistics/detail-list`,
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

//获取折线图
export const getLineChart = (params) => {
  return request({
    url: `/api/blade-report/pay-channel-statistics/daily-amount`,
    method: 'get',
    params: params
  })
}

export const getPieChart = (params) => {
  return request({
    url: `/api/blade-report/pay-channel-statistics/pie-chart`,
    method: 'get',
    params: params
  })
}




