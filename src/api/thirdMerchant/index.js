import request from '@/router/axios';

export const getList = (current, size, params) => {
    return request({
        url: '/api/lecent-park/admin/parklot/merchant/page',
        method: 'get',
        params: {
            ...params,
            current,
            size,
        }
    })
}
// 
export const getMerchantList = (params) => {
    return request({
        url: '/api/lecent-park/admin/parklot/merchant/list',
        method: 'get',
        params
    })
}

export const save = (data) => {
    return request({
        url: '/api/lecent-park/admin/parklot/merchant/save',
        method: 'post',
        data
    })
}
export const update = (data) => {
    return request({
        url: '/api/lecent-park/admin/parklot/merchant/update',
        method: 'post',
        data
    })
}

export const remove = (id) => {
    return request({
        url: '/api/lecent-park/admin/parklot/merchant/delete',
        method: 'post',
        params: {
            id
        }
    })
}

export const updateStatus = (id, status) => {
    return request({
        url: '/api/lecent-park/admin/parklot/third-merchant/update-status',
        method: 'post',
        params: {
            id,
            status
        }
    })
}