import request from "@/router/axios";
import { getToken } from "@/util/auth";
export const personnelVisitingList = params => {
  return request({
    url: "/api/lecent-door/visit/log/list",
    method: "get",
    params
  });
};

export const addPersonnelVisiting = data => {
  return request({
    url: "/api/lecent-door/visit/log/save",
    method: "post",
    data
  });
};

export const allowPersonnelVisiting = (data) => {
  return request({
    url: "/api/lecent-door/visit/log/update/doorValidityDate",
    method: "post",
    data: data
  });
};

export const updatePersonnelVisiting = data => {
  return request({
    url: "/api/lecent-door/visit/log/update",
    method: "post",
    data
  });
};

export const getVisitingRecords = params => {
  return request({
    url: "/api/lecent-door/visit/user/page",
    method: "get",
    params
  });
};

export const getAnomalyVisitUser = params => {
  return request({
    url: "/api/lecent-door/visit/user/anomalyVisitUser",
    method: "get",
    params
  });
};

export const getBlacklist = params => {
  return request({
    url: "/api/lecent-door/visit/user/blacklist/list",
    method: "get",
    params
  });
};

export const updateBlacklist = data => {
  return request({
    url: "/api/lecent-door/visit/user/update",
    method: "post",
    data
  });
};
export const getVisitDetail = params => {
  return request({
    url: "/api/lecent-door/visit/log/list",
    method: "get",
    params
  });
};

export const getAnomalyVisitLogDetail = params => {
  return request({
    url: "/api/lecent-door/visit/log/anomalyVisitLogDetail",
    method: "get",
    params
  });
};

export const exportTableExcel = (data, url) => {
  let lecentAuth = getToken();
  url = url + "?Blade-Auth=" + lecentAuth;
  // 创建form元素
  var temp_form = document.createElement("form");
  // 设置form属性
  temp_form.action = url;
  temp_form.target = "_self";
  temp_form.method = "post";
  temp_form.style.display = "none";
  // 处理需要传递的参数
  for (var x in data) {
    var opt = document.createElement("textarea");
    opt.name = x;
    opt.value = data[x];
    temp_form.appendChild(opt);
  }
  document.body.appendChild(temp_form);
  // 提交表单      u
  temp_form.submit();
};

export const getVisiting = (params) => {
  return request({
    url: "/api/lecent-door/visit/user/searchByPhone",
    method: "get",
    params: {
      ...params
    }
  });
};
