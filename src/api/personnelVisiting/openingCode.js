import request from "@/router/axios";
import { getToken } from "@/util/auth";
export const getOpencodeList = params => {
  return request({
    url: "/api/lecent-door/openCodeRecord/page",
    method: "get",
    params
  });
};

export const getOpenCodeStatistics = params => {
  return request({
    url: "/api/lecent-door/openCodeRecord/countPage",
    method: "get",
    params
  });
};

export const exportTableExcel = (data, url) => {
  let lecentAuth = getToken();
  url = url + "?Blade-Auth=" + lecentAuth;
  // 创建form元素
  var temp_form = document.createElement("form");
  // 设置form属性
  temp_form.action = url;
  temp_form.target = "_self";
  temp_form.method = "post";
  temp_form.style.display = "none";
  // 处理需要传递的参数
  for (var x in data) {
    var opt = document.createElement("textarea");
    opt.name = x;
    opt.value = data[x];
    temp_form.appendChild(opt);
  }
  document.body.appendChild(temp_form);
  // 提交表单      u
  temp_form.submit();
};
