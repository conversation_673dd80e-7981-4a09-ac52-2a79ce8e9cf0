import request from '@/router/axios';

export const getList = () => {
  return request({
    url: '/api/blade-report/placeExport/template/list',
    method: 'get',
  })
}

export const getTemplate = (templateId) => {
  return request({
    url: '/api/blade-report/placeExport/template',
    method: 'get',
    params:{
      id:templateId
    }
  })
}


export const exportQr = (parkLotIds, placeIds, templateIds) => {
  return request({
    url: '/api/blade-report/placeExport/qr-download',
    method: 'post',
    params:{
      parkLotIds,
      placeIds,
      templateIds
    }
  })
}
