import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/lecent-park/blacklist/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      status:1
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/lecent-park/blacklist/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/lecent-park/blacklist/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const customRemove = (row) => {
  return request({
    url: '/api/lecent-park/blacklist/remove',
    method: 'post',
    data: row
  })
}

export const customSubmit = (row) => {
  return request({
    url: '/api/lecent-park/blacklist/customSubmit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/lecent-park/blacklist/submit',
    method: 'post',
    data: row
  })
}

export const rollback = (row) => {
  return request({
    url: '/api/lecent-park/blacklist/rollback',
    method: 'post',
    data: row
  })
}
