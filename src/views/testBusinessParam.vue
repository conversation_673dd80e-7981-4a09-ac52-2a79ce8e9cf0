<template>
  <div class="test-page">
    <h1>业务参数管理功能测试</h1>
    <p>这是一个测试页面，用于验证业务参数管理功能是否正常工作。</p>
    
    <el-button type="primary" @click="goToBusinessParam">进入业务参数管理</el-button>
    
    <div class="test-info">
      <h3>测试说明：</h3>
      <ul>
        <li>点击上方按钮进入业务参数管理页面</li>
        <li>页面包含两个Tab：业务参数元数据管理和业务参数值管理</li>
        <li>当前使用Mock数据，可以测试增删改查功能</li>
        <li>数据字段包含您要求的所有字段</li>
      </ul>
      
      <h3>功能特性：</h3>
      <ul>
        <li>✅ 使用Tab区分元数据管理和参数值管理</li>
        <li>✅ <strong>左树右列表布局</strong> - 使用Element UI的Row/Col布局</li>
        <li>✅ <strong>标准Element组件</strong> - 使用el-card、el-tree、el-tag等标准组件</li>
        <li>✅ 支持业务分类、参数级别（平台级/租户级）、参数类型等字段</li>
        <li>✅ 支持验证规则（JSON格式）</li>
        <li>✅ 支持版本管理和显示顺序</li>
        <li>✅ 支持租户隔离和应用隔离</li>
        <li>✅ 完整的CRUD操作</li>
        <li>✅ 搜索和分页功能</li>
        <li>✅ 分类筛选功能</li>
        <li>✅ 分类数量统计</li>
        <li>✅ 分类树增删改功能（已修复显示问题）</li>
        <li>✅ 分类搜索功能（使用avue-tree自带搜索）</li>
        <li>✅ 全局字体一致性</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestBusinessParam',
  methods: {
    goToBusinessParam() {
      this.$router.push('/system/businessParam');
    }
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-info {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.test-info h3 {
  color: #333;
  margin-bottom: 10px;
}

.test-info ul {
  margin-left: 20px;
}

.test-info li {
  margin-bottom: 5px;
  line-height: 1.6;
}
</style> 