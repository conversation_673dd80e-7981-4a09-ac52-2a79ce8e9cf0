<template>
  <basic-container>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>业务参数管理</span>
      </div>
      
      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="业务参数元数据管理" name="metadata">
          <el-row :gutter="20">
            <!-- 左侧分类树 -->
            <el-col :span="4">
              <el-card shadow="never" style="height: calc(100vh - 280px); overflow-y: auto;">
                <div slot="header" class="clearfix" style="padding: 0; border: none; background: none;">
                  <span>业务参数分类</span>
                  <el-button style="float: right; padding: 3px 0" type="text" @click="refreshCategoryTree">
                    <i class="el-icon-refresh"></i>
                  </el-button>
                </div>
                
                <avue-tree
                  :option="treeOption"
                  :data="categoryTree"
                  @node-click="handleCategoryClick"
                  @addBtn="handleTreeSave"
                  @delBtn="handleTreeDelete"
                  @editBtn="handleTreeUpdate"
                  ref="categoryTree"
                  v-model="treeForm">
                  <template slot="menu" slot-scope="scope">
                    <el-button size="small" type="text" icon="el-icon-plus" @click.stop="handleAddCategory(scope.row)">新增</el-button>
                    <el-button size="small" type="text" icon="el-icon-edit" @click.stop="handleEditCategory(scope.row)">编辑</el-button>
                    <el-button size="small" type="text" icon="el-icon-delete" @click.stop="handleDeleteCategory(scope.row)">删除</el-button>
                  </template>
                </avue-tree>
              </el-card>
            </el-col>
            
            <!-- 右侧参数列表 -->
            <el-col :span="20">
              <el-card shadow="never">
                <div slot="header" class="clearfix" style="padding: 0; border: none; background: none;">
                  <el-breadcrumb separator="/" class="breadcrumb-header">
                    <el-breadcrumb-item :to="{ path: '' }" @click.native="handleBreadcrumbClick('root')">
                      <i class="el-icon-menu"></i> 全部参数
                    </el-breadcrumb-item>
                    <el-breadcrumb-item 
                      v-for="(item, index) in currentCategoryPath.slice(0, -1)"
                      :key="item.id"
                      :to="{ path: '' }" 
                      @click.native="handleBreadcrumbClick(item.id)">
                      <i :class="item.icon"></i> {{ item.name }}
                    </el-breadcrumb-item>
                    <el-breadcrumb-item 
                      v-if="currentCategoryPath.length > 0">
                      <i :class="currentCategoryPath[currentCategoryPath.length - 1].icon"></i> 
                      {{ currentCategoryPath[currentCategoryPath.length - 1].name }}
                    </el-breadcrumb-item>
                  </el-breadcrumb>
                  <el-button 
                    style="float: right; padding: 3px 0" 
                    type="primary" 
                    size="small" 
                    @click="addMetadata" 
                    v-if="permission.business_param_add">
                    <i class="el-icon-plus"></i> 新增参数
                  </el-button>
                </div>
                
                <avue-crud 
                  :option="metadataOption"
                  :table-loading="metadataLoading"
                  :data="metadataData"
                  ref="metadataCrud"
                  v-model="metadataForm"
                  :page="metadataPage"
                  :permission="permissionList"
                  @row-del="metadataRowDel"
                  @row-update="metadataRowUpdate"
                  @row-save="metadataRowSave"
                  @search-change="metadataSearchChange"
                  @search-reset="metadataSearchReset"
                  @selection-change="metadataSelectionChange"
                  @current-change="metadataCurrentChange"
                  @size-change="metadataSizeChange"
                  @refresh-change="metadataRefreshChange"
                  @on-load="metadataOnLoad">
                  <template slot="menuLeft">
                    <el-button type="danger"
                               size="small"
                               icon="el-icon-delete"
                               v-if="permission.business_param_delete"
                               plain
                               @click="handleMetadataDelete">删 除
                    </el-button>
                  </template>
                </avue-crud>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        
        <el-tab-pane label="业务参数值管理" name="value">
          <avue-crud 
            :option="valueOption"
            :table-loading="valueLoading"
            :data="valueData"
            ref="valueCrud"
            v-model="valueForm"
            :page="valuePage"
            :permission="permissionList"
            @row-del="valueRowDel"
            @row-update="valueRowUpdate"
            @row-save="valueRowSave"
            @search-change="valueSearchChange"
            @search-reset="valueSearchReset"
            @selection-change="valueSelectionChange"
            @current-change="valueCurrentChange"
            @size-change="valueSizeChange"
            @refresh-change="valueRefreshChange"
            @on-load="valueOnLoad">
            <template slot="menuLeft">
              <el-button type="danger"
                         size="small"
                         icon="el-icon-delete"
                         v-if="permission.business_param_delete"
                         plain
                         @click="handleValueDelete">删 除
              </el-button>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </basic-container>
</template>

<script>
  import {getMetadataList, removeMetadata, submitMetadata, updateMetadata} from "@/api/system/businessParam";
  import {getValueList, removeValue, submitValue, updateValue} from "@/api/system/businessParam";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        activeTab: 'metadata',
        
        // 分类树相关
        categoryTree: [],
        categoryProps: {
          children: 'children',
          label: 'name'
        },
        currentCategory: 'root',
        currentCategoryName: '全部参数',
        currentCategoryPath: [],
        treeForm: {},
        treeOption: {
          nodeKey: 'id',
          lazy: false,
          addBtn: true,
          editBtn: true,
          delBtn: true,
          menu: true,
          menuType: 'text',
          size: 'small',
          defaultExpandAll: true,
          searchShow: true,
          searchPlaceholder: '搜索分类',
          props: {
            labelText: '分类名称',
            label: 'name',
            value: 'value',
            children: 'children',
          },
          column: [
            {
              label: '分类名称',
              prop: 'name',
              rules: [{
                required: true,
                message: '请输入分类名称',
                trigger: 'blur'
              }]
            },
            {
              label: '图标',
              prop: 'icon',
              type: 'select',
              dicData: [
                {label: '文件夹', value: 'el-icon-folder'},
                {label: '菜单', value: 'el-icon-menu'},
                {label: '金钱', value: 'el-icon-money'},
                {label: '位置', value: 'el-icon-location'},
                {label: '信用卡', value: 'el-icon-credit-card'},
                {label: '设置', value: 'el-icon-setting'},
                {label: '时间', value: 'el-icon-time'},
                {label: '通知', value: 'el-icon-bell'},
                {label: '硬币', value: 'el-icon-coin'},
                {label: '警告', value: 'el-icon-warning'},
                {label: '连接', value: 'el-icon-connection'},
                {label: '锁', value: 'el-icon-lock'},
                {label: '文档', value: 'el-icon-document'},
                {label: '文档复制', value: 'el-icon-document-copy'}
              ]
            }
          ]
        },
        
        // 元数据管理相关
        metadataForm: {},
        metadataSelectionList: [],
        metadataQuery: {},
        metadataLoading: true,
        metadataPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        metadataData: [],
        metadataOption: {
          height: 'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          indexLabel:'序号',
          border: true,
          index: true,
          selection: true,
          searchMenuSpan: 6,
          viewBtn: true,
          dialogClickModal: false,
          column: [
            {
              label: "业务分类",
              prop: "businessCategory",
              search: true,
              span: 12,
              type: "select",
              dicData: [
                {label: "追缴业务", value: "COLLECTION_BUSINESS"},
                {label: "停车业务", value: "PARKING_BUSINESS"},
                {label: "支付业务", value: "PAYMENT_BUSINESS"},
                {label: "系统业务", value: "SYSTEM_BUSINESS"}
              ],
              rules: [{
                required: true,
                message: "请选择业务分类",
                trigger: "change"
              }]
            },
            {
              label: "参数级别",
              prop: "paramLevel",
              search: true,
              span: 12,
              type: "select",
              dicData: [
                {label: "平台级", value: "1"},
                {label: "租户级", value: "2"}
              ],
              rules: [{
                required: true,
                message: "请选择参数级别",
                trigger: "change"
              }]
            },
            {
              label: "参数键",
              prop: "paramKey",
              search: true,
              span: 12,
              rules: [{
                required: true,
                message: "请输入参数键",
                trigger: "blur"
              }]
            },
            {
              label: "参数名称",
              prop: "paramName",
              search: true,
              span: 12,
              rules: [{
                required: true,
                message: "请输入参数名称",
                trigger: "blur"
              }]
            },
            {
              label: "参数类型",
              prop: "paramType",
              span: 12,
              type: "select",
              dicData: [
                {label: "字符串", value: "STRING"},
                {label: "数字", value: "NUMBER"},
                {label: "布尔值", value: "BOOLEAN"},
                {label: "日期", value: "DATE"},
                {label: "JSON", value: "JSON"}
              ],
              rules: [{
                required: true,
                message: "请选择参数类型",
                trigger: "change"
              }]
            },
            {
              label: "默认值",
              prop: "defaultValue",
              span: 12,
              rules: [{
                required: false,
                message: "请输入默认值",
                trigger: "blur"
              }]
            },
            {
              label: "验证规则",
              prop: "validationRule",
              span: 24,
              type: "textarea",
              minRows: 3,
              rules: [{
                required: false,
                message: "请输入验证规则（JSON格式）",
                trigger: "blur"
              }]
            },
            {
              label: "参数描述",
              prop: "description",
              span: 24,
              type: "textarea",
              minRows: 3,
              rules: [{
                required: false,
                message: "请输入参数描述",
                trigger: "blur"
              }]
            },
            {
              label: "是否必填",
              prop: "isRequired",
              span: 12,
              type: "select",
              dicData: [
                {label: "是", value: "1"},
                {label: "否", value: "0"}
              ],
              rules: [{
                required: true,
                message: "请选择是否必填",
                trigger: "change"
              }]
            },
            {
              label: "显示顺序",
              prop: "displayOrder",
              span: 12,
              type: "number",
              rules: [{
                required: true,
                message: "请输入显示顺序",
                trigger: "blur"
              }]
            },
            {
              label: "版本号",
              prop: "version",
              span: 12,
              type: "number",
              rules: [{
                required: true,
                message: "请输入版本号",
                trigger: "blur"
              }]
            }
          ]
        },
        
        // 参数值管理相关
        valueForm: {},
        valueSelectionList: [],
        valueQuery: {},
        valueLoading: true,
        valuePage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        valueData: [],
        valueOption: {
          height: 'auto',
          calcHeight: 30,
          tip: false,
          searchShow: true,
          indexLabel:'序号',
          border: true,
          index: true,
          selection: true,
          searchMenuSpan: 6,
          viewBtn: true,
          dialogClickModal: false,
          column: [
            {
              label: "参数键",
              prop: "paramKey",
              search: true,
              span: 12,
              rules: [{
                required: true,
                message: "请输入参数键",
                trigger: "blur"
              }]
            },
            {
              label: "参数名称",
              prop: "paramName",
              search: true,
              span: 12,
              rules: [{
                required: true,
                message: "请输入参数名称",
                trigger: "blur"
              }]
            },
            {
              label: "参数值",
              prop: "paramValue",
              span: 24,
              type: "textarea",
              minRows: 4,
              rules: [{
                required: true,
                message: "请输入参数值",
                trigger: "blur"
              }]
            },
            {
              label: "租户ID",
              prop: "tenantId",
              span: 12,
              rules: [{
                required: false,
                message: "请输入租户ID",
                trigger: "blur"
              }]
            },
            {
              label: "应用ID",
              prop: "appId",
              span: 12,
              rules: [{
                required: false,
                message: "请输入应用ID",
                trigger: "blur"
              }]
            },
            {
              label: "备注",
              prop: "remark",
              span: 24,
              type: "textarea",
              minRows: 3,
              rules: [{
                required: false,
                message: "请输入备注",
                trigger: "blur"
              }]
            }
          ]
        }
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.business_param_add, false),
          viewBtn: this.vaildData(this.permission.business_param_view, false),
          delBtn: this.vaildData(this.permission.business_param_delete, false),
          editBtn: this.vaildData(this.permission.business_param_edit, false)
        };
      },
      metadataIds() {
        let ids = [];
        this.metadataSelectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      valueIds() {
        let ids = [];
        this.valueSelectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 分类树相关方法
      initCategoryTree() {
        // 初始化分类树数据
        this.categoryTree = [
          {
            id: 'root',
            name: '全部参数',
            icon: 'el-icon-menu',
            count: 0
          },
          {
            id: 'COLLECTION_BUSINESS',
            name: '追缴业务',
            icon: 'el-icon-money',
            count: 0,
            children: [
              {
                id: 'COLLECTION_BUSINESS.timeout',
                name: '超时设置',
                icon: 'el-icon-time',
                count: 0
              },
              {
                id: 'COLLECTION_BUSINESS.notification',
                name: '通知设置',
                icon: 'el-icon-bell',
                count: 0
              }
            ]
          },
          {
            id: 'PARKING_BUSINESS',
            name: '停车业务',
            icon: 'el-icon-location',
            count: 0,
            children: [
              {
                id: 'PARKING_BUSINESS.fee',
                name: '费用设置',
                icon: 'el-icon-coin',
                count: 0
              },
              {
                id: 'PARKING_BUSINESS.limit',
                name: '限制设置',
                icon: 'el-icon-warning',
                count: 0
              }
            ]
          },
          {
            id: 'PAYMENT_BUSINESS',
            name: '支付业务',
            icon: 'el-icon-credit-card',
            count: 0,
            children: [
              {
                id: 'PAYMENT_BUSINESS.gateway',
                name: '网关设置',
                icon: 'el-icon-connection',
                count: 0
              },
              {
                id: 'PAYMENT_BUSINESS.security',
                name: '安全设置',
                icon: 'el-icon-lock',
                count: 0
              }
            ]
          },
          {
            id: 'SYSTEM_BUSINESS',
            name: '系统业务',
            icon: 'el-icon-setting',
            count: 0,
            children: [
              {
                id: 'SYSTEM_BUSINESS.log',
                name: '日志设置',
                icon: 'el-icon-document',
                count: 0
              },
              {
                id: 'SYSTEM_BUSINESS.metadata',
                name: '业务参数元数据',
                icon: 'el-icon-document-copy',
                count: 0
              }
            ]
          }
        ];
        
        // 更新分类数量
        this.updateCategoryCounts();
      },
      
      updateCategoryCounts() {
        // 模拟更新分类数量
        this.categoryTree.forEach(category => {
          if (category.children) {
            category.children.forEach(subCategory => {
              subCategory.count = Math.floor(Math.random() * 10) + 1;
            });
            category.count = category.children.reduce((sum, child) => sum + child.count, 0);
          }
        });
      },
      
      handleCategoryClick(data) {
        this.currentCategory = data.id;
        this.currentCategoryName = data.name;
        
        // 更新分类路径
        this.updateCategoryPath(data);
        
        // 根据分类筛选数据
        if (data.id === 'root') {
          this.metadataQuery.businessCategory = '';
        } else if (data.children) {
          // 如果是父分类，筛选该分类下的所有子分类
          this.metadataQuery.businessCategory = data.id;
        } else {
          // 如果是子分类，精确筛选
          this.metadataQuery.businessCategory = data.id;
        }
        
        this.metadataPage.currentPage = 1;
        this.metadataOnLoad(this.metadataPage);
      },
      
      updateCategoryPath(data) {
        this.currentCategoryPath = [];
        
        if (data.id === 'root') {
          return;
        }
        
        // 查找当前分类的完整路径
        const findPath = (categories, targetId, path = []) => {
          for (let category of categories) {
            const currentPath = [...path, category];
            
            if (category.id === targetId) {
              this.currentCategoryPath = currentPath;
              return true;
            }
            
            if (category.children && findPath(category.children, targetId, currentPath)) {
              return true;
            }
          }
          return false;
        };
        
        findPath(this.categoryTree, data.id);
      },
      
      handleBreadcrumbClick(categoryId) {
        // 根据分类ID查找分类数据
        const findCategory = (categories, targetId) => {
          for (let category of categories) {
            if (category.id === targetId) {
              return category;
            }
            if (category.children) {
              const found = findCategory(category.children, targetId);
              if (found) return found;
            }
          }
          return null;
        };
        
        const categoryData = findCategory(this.categoryTree, categoryId);
        if (categoryData) {
          this.handleCategoryClick(categoryData);
        }
      },
      
      refreshCategoryTree() {
        this.updateCategoryCounts();
        this.$message({
          type: 'success',
          message: '分类树已刷新'
        });
      },
      
      // 树操作相关方法
      handleTreeSave(row, done, loading) {
        // 模拟保存操作
        setTimeout(() => {
          this.$message({
            type: 'success',
            message: '分类保存成功'
          });
          done();
        }, 1000);
      },
      
      handleTreeDelete(row) {
        this.$confirm('确定删除该分类吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$message({
            type: 'success',
            message: '分类删除成功'
          });
        });
      },
      
      handleTreeUpdate(row, index, done, loading) {
        // 模拟更新操作
        setTimeout(() => {
          this.$message({
            type: 'success',
            message: '分类更新成功'
          });
          done();
        }, 1000);
      },
      
      handleAddCategory(row) {
        this.$refs.categoryTree.rowAdd(row);
      },
      
      handleEditCategory(row) {
        this.$refs.categoryTree.rowUpdate(row);
      },
      
      handleDeleteCategory(row) {
        this.$refs.categoryTree.rowDel(row);
      },
      
      addMetadata() {
        // 新增参数时，自动设置当前选中的分类
        if (this.currentCategory !== 'root') {
          this.metadataForm.businessCategory = this.currentCategory;
        } else {
          // 如果选择的是"全部参数"，清空分类设置
          this.metadataForm.businessCategory = '';
        }
        this.$refs.metadataCrud.rowAdd();
      },
      
      handleTabClick(tab) {
        if (tab.name === 'metadata') {
          this.metadataOnLoad(this.metadataPage);
        } else if (tab.name === 'value') {
          this.valueOnLoad(this.valuePage);
        }
      },
      
      // 元数据管理方法
      metadataRowSave(row, done, loading) {
        submitMetadata(row).then(() => {
          this.metadataOnLoad(this.metadataPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      metadataRowUpdate(row, index, done, loading) {
        updateMetadata(row).then(() => {
          this.metadataOnLoad(this.metadataPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      metadataRowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeMetadata(row.id);
          })
          .then(() => {
            this.metadataOnLoad(this.metadataPage);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      metadataSearchReset() {
        this.metadataQuery = {};
        this.metadataOnLoad(this.metadataPage);
      },
      metadataSearchChange(params, done) {
        this.metadataQuery = params;
        this.metadataPage.currentPage = 1;
        this.metadataOnLoad(this.metadataPage, params);
        done();
      },
      metadataSelectionChange(list) {
        this.metadataSelectionList = list;
      },
      metadataSelectionClear() {
        this.metadataSelectionList = [];
        this.$refs.metadataCrud.toggleSelection();
      },
      handleMetadataDelete() {
        if (this.metadataSelectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeMetadata(this.metadataIds);
          })
          .then(() => {
            this.metadataOnLoad(this.metadataPage);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.metadataCrud.toggleSelection();
          });
      },
      metadataCurrentChange(currentPage) {
        this.metadataPage.currentPage = currentPage;
      },
      metadataSizeChange(pageSize) {
        this.metadataPage.pageSize = pageSize;
      },
      metadataRefreshChange() {
        this.metadataOnLoad(this.metadataPage, this.metadataQuery);
      },
      metadataOnLoad(page, params = {}) {
        this.metadataLoading = true;
        getMetadataList(page.currentPage, page.pageSize, Object.assign(params, this.metadataQuery)).then(res => {
          const data = res.data.data;
          this.metadataPage.total = data.total;
          this.metadataData = data.records;
          this.metadataLoading = false;
          this.metadataSelectionClear();
        });
      },
      
      // 参数值管理方法
      valueRowSave(row, done, loading) {
        submitValue(row).then(() => {
          this.valueOnLoad(this.valuePage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      valueRowUpdate(row, index, done, loading) {
        updateValue(row).then(() => {
          this.valueOnLoad(this.valuePage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done();
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      valueRowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeValue(row.id);
          })
          .then(() => {
            this.valueOnLoad(this.valuePage);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      valueSearchReset() {
        this.valueQuery = {};
        this.valueOnLoad(this.valuePage);
      },
      valueSearchChange(params, done) {
        this.valueQuery = params;
        this.valuePage.currentPage = 1;
        this.valueOnLoad(this.valuePage, params);
        done();
      },
      valueSelectionChange(list) {
        this.valueSelectionList = list;
      },
      valueSelectionClear() {
        this.valueSelectionList = [];
        this.$refs.valueCrud.toggleSelection();
      },
      handleValueDelete() {
        if (this.valueSelectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeValue(this.valueIds);
          })
          .then(() => {
            this.valueOnLoad(this.valuePage);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.valueCrud.toggleSelection();
          });
      },
      valueCurrentChange(currentPage) {
        this.valuePage.currentPage = currentPage;
      },
      valueSizeChange(pageSize) {
        this.valuePage.pageSize = pageSize;
      },
      valueRefreshChange() {
        this.valueOnLoad(this.valuePage, this.valueQuery);
      },
      valueOnLoad(page, params = {}) {
        this.valueLoading = true;
        getValueList(page.currentPage, page.pageSize, Object.assign(params, this.valueQuery)).then(res => {
          const data = res.data.data;
          this.valuePage.total = data.total;
          this.valueData = data.records;
          this.valueLoading = false;
          this.valueSelectionClear();
        });
      }
    },
    mounted() {
      this.initCategoryTree();
      this.metadataOnLoad(this.metadataPage);
    }
  };
</script>

<style scoped>
.el-tabs {
  margin-top: 10px;
}

.el-card__header {
    padding: 15px 20px;
    margin-bottom: 15px;
}

/* 确保树组件字体与全局一致 */
.avue-tree {
  font-family: inherit;
}

.avue-tree .el-tree-node__content {
  font-size: 14px;
  font-family: inherit;
}

.avue-tree .el-tree-node__label {
  font-family: inherit;
}

/* 搜索框样式 */
.el-input--small .el-input__inner {
  font-size: 14px;
  font-family: inherit;
}

/* 面包屑样式 */
.breadcrumb-header {
  display: inline-block;
  vertical-align: middle;
}

.breadcrumb-header .el-breadcrumb__item {
  font-size: 14px;
}

.breadcrumb-header .el-breadcrumb__inner {
  color: #606266;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s;
}

.breadcrumb-header .el-breadcrumb__inner:hover {
  color: #409EFF;
}

.breadcrumb-header .el-breadcrumb__inner i {
  margin-right: 4px;
  font-size: 14px;
}

.breadcrumb-header .el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: #303133;
  font-weight: 600;
  cursor: default;
}

.breadcrumb-header .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: #303133;
}

/* 清除卡片头部左边框样式 */
::v-deep .el-card__header {
  border-left: none;
}

</style> 