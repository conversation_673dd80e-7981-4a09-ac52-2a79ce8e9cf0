<template>
  <basic-container>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>业务参数管理</span>
      </div>

      <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="业务参数元数据管理" name="metadata">
          <metadata-management ref="metadataManagement" />
        </el-tab-pane>

        <el-tab-pane label="业务参数值管理" name="value">
          <value-management ref="valueManagement" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </basic-container>
</template>

<script>
import MetadataManagement from './businessParam/MetadataManagement.vue';
import ValueManagement from './businessParam/ValueManagement.vue';

export default {
  name: 'BusinessParam',
  components: {
    MetadataManagement,
    ValueManagement
  },
  data() {
    return {
      activeTab: 'metadata'
    };
  },
  methods: {
    handleTabClick(tab) {
      // Tab切换时可以在这里添加特定逻辑
      console.log('切换到tab:', tab.name);
    }
  }
};
</script>

<style scoped>
.el-tabs {
  margin-top: 10px;
}

.el-card__header {
  padding: 15px 20px;
  margin-bottom: 15px;
}
</style>