<template>
  <avue-crud 
    :option="valueOption"
    :table-loading="valueLoading"
    :data="valueData"
    ref="valueCrud"
    v-model="valueForm"
    :page="valuePage"
    :permission="permissionList"
    @row-del="valueRowDel"
    @row-update="valueRowUpdate"
    @row-save="valueRowSave"
    @search-change="valueSearchChange"
    @search-reset="valueSearchReset"
    @selection-change="valueSelectionChange"
    @current-change="valueCurrentChange"
    @size-change="valueSizeChange"
    @refresh-change="valueRefreshChange"
    @on-load="valueOnLoad">
    <template slot="menuLeft">
      <el-button type="danger"
                 size="small"
                 icon="el-icon-delete"
                 v-if="permission.business_param_delete"
                 plain
                 @click="handleValueDelete">删 除
      </el-button>
    </template>
  </avue-crud>
</template>

<script>
import {getValueList, removeValue, submitValue, updateValue} from "@/api/system/businessParam";
import {mapGetters} from "vuex";

export default {
  name: 'ValueManagement',
  data() {
    return {
      // 参数值管理相关
      valueForm: {},
      valueSelectionList: [],
      valueQuery: {},
      valueLoading: true,
      valuePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      valueData: [],
      valueOption: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        indexLabel:'序号',
        border: true,
        index: true,
        selection: true,
        searchMenuSpan: 6,
        viewBtn: true,
        dialogClickModal: false,
        column: [
          {
            label: "参数键",
            prop: "paramKey",
            search: true,
            span: 12,
            rules: [{
              required: true,
              message: "请输入参数键",
              trigger: "blur"
            }]
          },
          {
            label: "参数名称",
            prop: "paramName",
            search: true,
            span: 12,
            rules: [{
              required: true,
              message: "请输入参数名称",
              trigger: "blur"
            }]
          },
          {
            label: "参数值",
            prop: "paramValue",
            span: 24,
            type: "textarea",
            minRows: 4,
            rules: [{
              required: true,
              message: "请输入参数值",
              trigger: "blur"
            }]
          },
          {
            label: "租户ID",
            prop: "tenantId",
            span: 12,
            rules: [{
              required: false,
              message: "请输入租户ID",
              trigger: "blur"
            }]
          },
          {
            label: "应用ID",
            prop: "appId",
            span: 12,
            rules: [{
              required: false,
              message: "请输入应用ID",
              trigger: "blur"
            }]
          },
          {
            label: "备注",
            prop: "remark",
            span: 24,
            type: "textarea",
            minRows: 3,
            rules: [{
              required: false,
              message: "请输入备注",
              trigger: "blur"
            }]
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.business_param_add, false),
        viewBtn: this.vaildData(this.permission.business_param_view, false),
        delBtn: this.vaildData(this.permission.business_param_delete, false),
        editBtn: this.vaildData(this.permission.business_param_edit, false)
      };
    },
    valueIds() {
      let ids = [];
      this.valueSelectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    // 参数值管理方法
    valueRowSave(row, done, loading) {
      submitValue(row).then(() => {
        this.valueOnLoad(this.valuePage);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    
    valueRowUpdate(row, index, done, loading) {
      updateValue(row).then(() => {
        this.valueOnLoad(this.valuePage);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    
    valueRowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return removeValue(row.id);
        })
        .then(() => {
          this.valueOnLoad(this.valuePage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    
    valueSearchReset() {
      this.valueQuery = {};
      this.valueOnLoad(this.valuePage);
    },
    
    valueSearchChange(params, done) {
      this.valueQuery = params;
      this.valuePage.currentPage = 1;
      this.valueOnLoad(this.valuePage, params);
      done();
    },
    
    valueSelectionChange(list) {
      this.valueSelectionList = list;
    },
    
    valueSelectionClear() {
      this.valueSelectionList = [];
      this.$refs.valueCrud.toggleSelection();
    },
    
    handleValueDelete() {
      if (this.valueSelectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return removeValue(this.valueIds);
        })
        .then(() => {
          this.valueOnLoad(this.valuePage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.valueCrud.toggleSelection();
        });
    },
    
    valueCurrentChange(currentPage) {
      this.valuePage.currentPage = currentPage;
    },
    
    valueSizeChange(pageSize) {
      this.valuePage.pageSize = pageSize;
    },
    
    valueRefreshChange() {
      this.valueOnLoad(this.valuePage, this.valueQuery);
    },
    
    valueOnLoad(page, params = {}) {
      this.valueLoading = true;
      getValueList(page.currentPage, page.pageSize, Object.assign(params, this.valueQuery)).then(res => {
        const data = res.data.data;
        this.valuePage.total = data.total;
        this.valueData = data.records;
        this.valueLoading = false;
        this.valueSelectionClear();
      });
    }
  },
  mounted() {
    this.valueOnLoad(this.valuePage);
  }
};
</script>

<style scoped>
/* 组件特定样式 */
</style>
