<template>
  <el-form>
    <el-form-item>
      <mark class="_mark" v-show="showSeason" @click.stop="showSeason=false"></mark>
      <el-input placeholder="请选择季度" v-model="showValue" size="small" style="width:12.7rem" @focus="showSeason=true" :disabled="disabled">
        <i slot="prefix" class="el-input__icon el-icon-date"></i>
      </el-input>

      <el-card class="box-card" v-show="showSeason">
        <div slot="header" class="clearfix yearBtn" @onclick.stop="''">
          <button type="button" aria-label="前一年" class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left" @click="prev"></button>
          <span role="button" class="el-date-picker__header-label">{{year}}年</span>
          <button type="button" aria-label="后一年" class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right" @click="next"></button>
        </div>
        <div class="text item">
          <el-button type="text" size="medium" class="_left" :class="villageForm.pubBizInterval==1?'btn-class':''" @click="selectSeason(0)" :disabled="disabled0">第一季度</el-button>
          <el-button type="text" size="medium" class="_right" :class="villageForm.pubBizInterval==2?'btn-class':''" @click="selectSeason(1)" :disabled="disabled1">第二季度</el-button>
        </div>
        <div class="text item">
          <el-button type="text" size="medium" class="_left" :class="villageForm.pubBizInterval==3?'btn-class':''" @click="selectSeason(2)" :disabled="disabled2">第三季度</el-button>
          <el-button type="text" size="medium" class="_right" :class="villageForm.pubBizInterval==4?'btn-class':''" @click="selectSeason(3)" :disabled="disabled3">第四季度</el-button>
        </div>
      </el-card>
    </el-form-item>
  </el-form>
</template>
<script>
export default {
  props:{
    valueArr:{
      default:()=>{
        return ["01-03","04-06","07-09","10-12"]
      },
      type:Array
    },
    getValue:{
      default:()=>{},
      type:Function
    },
    defaultValue:{
      type:String,
      default:''
    },
    disabled:'',
    // villageForm:{
    //   default:()=>{
    //     return {}
    //   },
    //   type:Object
    // }
  },
  data(){
    return {
      showSeason:false,
      season:"",
      year:new Date().getFullYear(),
      showValue:"",
      disabled0:false,
      disabled1:false,
      disabled2:false,
      disabled3:false,
      villageForm:{
        year:'',
        pubBizInterval:'',
        showValue:'',
      }
    }
  },
  created(){

    if(this.defaultValue){
      let value=this.defaultValue;
      let arr=value.split("-");
      this.year=arr[0].slice(0,4);
      let str=arr[0].slice(4,6)+"-"+arr[1].slice(4,6);
      let arrAll=this.valueArr;
      this.showValue=`${this.year}年${arrAll.indexOf(str)+1}季度`;
    }

    if(!this.showValue){
      this.year = new Date().getFullYear()
      this.showValue=`${this.year}年第一季度`
      this.villageForm.showValue =  this.showValue
      this.villageForm.year =  this.year
      this.villageForm.pubBizInterval =  1
      console.log(this.showValue, this.villageForm)
      this. sendMsg()
    }

    // if(this.villageForm){
    //   if(this.villageForm.pubBizInterval&&this.villageForm.pubBizInterval.substring(5,7)=="04"&&this.villageForm.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=this.villageForm.pubBizInterval.substring(0,4)+"第一季度"
    //   }else if(this.villageForm.pubBizInterval&&this.villageForm.pubBizInterval.substring(5,7)=="07"&&this.villageForm.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=this.villageForm.pubBizInterval.substring(0,4)+"第二季度"
    //   }else if(this.villageForm.pubBizInterval&&this.villageForm.pubBizInterval.substring(5,7)=="10"&&this.villageForm.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=this.villageForm.pubBizInterval.substring(0,4)+"第三季度"
    //   }else if(this.villageForm.pubBizInterval&&this.villageForm.pubBizInterval.substring(5,7)=="01"&&this.villageForm.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=parseInt(this.villageForm.pubBizInterval.substring(0,4))-parseInt(1)+"第四季度"
    //   }
    // }
  },
  watch:{
    defaultValue:function(value){
      let arr=value.split("-");
      this.year=arr[0].slice(0,4);
      let str=arr[0].slice(4,6)+"-"+arr[1].slice(4,6);
      let arrAll=this.valueArr;
      this.showValue=`${this.year}年${arrAll.indexOf(str)+1}季度`
    },
    showSeason:function (val){
      console.log(val, this.showValue)

    }
    // villageForm:function(value){
    //   if(value.pubBizInterval.substring(5,7)=="04"&&value.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=value.pubBizInterval.substring(0,4)+'第一季度'
    //   }else if(value.pubBizInterval.substring(5,7)=="07"&&value.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=value.pubBizInterval.substring(0,4)+'第二季度'
    //   }else if(value.pubBizInterval.substring(5,7)=="10"&&value.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=value.pubBizInterval.substring(0,4)+'第三季度'
    //   }else if(value.pubBizInterval.substring(5,7)=="01"&&value.pubBizInterval.substring(8,10)=="15"){
    //     this.showValue=parseInt(value.pubBizInterval.substring(0,4))-parseInt(1)+'第四季度'
    //   }
    // }
  },
  mounted(){
    this.getYear()
  },
  methods:{
    one(){
      this.showSeason=false
    },
    prev(){
      this.year=this.year*1-1;
      this.getYear()
    },
    next(){
      this.year=this.year*1+1;
      this.getYear()
    },
    reast(){
      this.showValue=''
    },
    getYear(){
      if(this.year>new Date().getFullYear()){
        this.disabled0=true
        this.disabled1=true
        this.disabled2=true
        this.disabled3=true
      }else if(this.year==new Date().getFullYear()){
        let month =  new Date().getMonth()+1
        console.log(month)
        if(month>9){
          this.disabled0=false
          this.disabled1=false
          this.disabled2=false
          this.disabled3=false
        }else if(month>6){
          this.disabled0=false
          this.disabled1=false
          this.disabled2=false
          this.disabled3=true
        }else if(month>3){
          this.disabled0=false
          this.disabled1=false
          this.disabled2=true
          this.disabled3=true
        }else{
          this.disabled0=false
          this.disabled1=true
          this.disabled2=true
          this.disabled3=true
        }
      }else{
        this.disabled0=false
        this.disabled1=false
        this.disabled2=false
        this.disabled3=false
      }
    },
    selectSeason(i){
      let that=this;
      that.season=i+1;
      let arr=that.valueArr[i].split("-");
      that.getValue(that.year+arr[0]+"-"+that.year+arr[1]);
      that.showSeason=false;
      if(this.season==1){
        this.showValue=`${this.year}年第一季度`
        this.villageForm.showValue =  this.showValue
        this.villageForm.year =  this.year
        this.villageForm.pubBizInterval =  1
      }else if(this.season==2){
        this.showValue=`${this.year}年第二季度`
        this.villageForm.showValue =  this.showValue
        this.villageForm.year =  this.year
        this.villageForm.pubBizInterval =  2
      }else if(this.season==3){
        this.showValue=`${this.year}年第三季度`
        this.villageForm.showValue =  this.showValue
        this.villageForm.year =  this.year
        this.villageForm.pubBizInterval =  3
      }else if(this.season==4){
        this.showValue=`${this.year}年第四季度`
        this.villageForm.showValue =  this.showValue
        this.villageForm.year =  this.year
        this.villageForm.pubBizInterval =  4
      }
      that.sendMsg()
    },
    getLastDay(year,month){
      var new_year=year;
      var new_month=month++;
      if(month>12){
        new_month-=12;
        new_year++
      }
      var new_date=new Date(new_year,new_month,1)
      return new Date(new_date.getTime()-1000*60*60*24).getDate()
    },
    sendMsg(){
      this.$emit("func",this.villageForm)
    }
  }
}
</script>
<style scoped lang="scss">
._mark{
  position: fixed;
  top:0;
  bottom:0;
  left:0;
  right:0;
  background: rgba(0,0,0,0);
  z-index:999;
}
.el-card.is-always-shadow,
.el-card.is-hover-shadow:focus,
.el-card.is-hover-shadow:hover{
  width: 200px;
}
.yearBtn{
  text-align: center;
  padding: 0;
}
.box-card{
  width: 322px;
  padding:0 3px 20px;
  margin-top:10px;
  position: fixed;
  z-index:9999;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  border: 1px solid #E4E7ED;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  background: #FFF;
  border-radius: 4px;
}
.el-date-picker__header-label{
  padding: 0 25px;
}
.text.item{
  text-align: center;
}

 .text.item >>> .el-button{
  text-align: center;
}
/deep/ .el-button{
 color: #606266;
}
 /deep/ .el-button--text{
   color: #606266;
 }

 /deep/ .el-button.is-disabled{
   color: #C0C4CC;
   cursor: not-allowed;
 }

.btn-class{
 color: #409EFF;
}

.text.item ._left{
  float: left;
}
.text.item ._right{
  float: right;
}
::v-deep .el-input--small .el-input__inner{
  height: 41px;
}
</style>
