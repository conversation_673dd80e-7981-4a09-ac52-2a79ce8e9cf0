<template>
  <div v-if="visible">
    //修改el-dialog z-index

    <el-dialog width="50%" :before-close="cancel" :closable="false" :mask-closable="false" :visible="visible"
               append-to-body :close-on-click-modal="false" >
			<span>
				<el-autocomplete v-model="addressKeyword" placeholder="请输入地址来直接查找相关位置" @input="mapInput" clearable
                         style="margin-bottom: 20px; width: 100%" :fetch-suggestions="querySearch"
                         @select="handleSelect">
<!--					<el-button slot="append" icon="el-icon-search" @click="getAddressKeyword"></el-button>-->
				</el-autocomplete>
				<div style="">
					选中地址:{{ shopInfo.addr }}
          <!-- [{{ shopInfo.lng }},{{ shopInfo.lat }}] -->
				</div>
				<div id="container" style="width: 100%; height: 400px"></div>
			</span>
      <span slot="footer" class="dialog-footer">
				<el-button @click="cancel">取 消</el-button>
				<el-button type="primary" @click="confirm">确 定</el-button>
			</span>
    </el-dialog>
  </div>
</template>

<script>
import {jsonp} from "vue-jsonp";

var markerLayer;
export default {
  components: {},
  data() {
    return {
      markersArray: [],
      restaurants: [],
      map: null,
      getAddress: null,
      getAddCode: null,
      addressKeyword: "",
      shopInfo: {
        lng: "",
        lat: "",
        addr: "",
      },
    };
  },
  props: {
    visible: {type: Boolean, default: false},
    lng: {},
    lat: {},
  },
  created() {
  },

  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.initTMap();

        if (this.lng & this.lat) {
          this.getMarker(this.lng, this.lat);
        }
      });
    },

    // 初始化地图
    initTMap() {
      const lat = sessionStorage.getItem("lat")
        ? Number(JSON.parse(sessionStorage.getItem("lat")).latitude)
        : null;
      const lng = sessionStorage.getItem("lat")
        ? Number(JSON.parse(sessionStorage.getItem("lat")).longitude)
        : null;
      var that = this;
      var center = new window.TMap.LatLng(26.612071, 106.647942);
      that.map = new window.TMap.Map(document.getElementById("container"), {
        center: center,
        zoom: 13,
        pitch: 0,
        rotation: 0,
      });
      that.map.on("click", function (event) {
        that.shopInfo.lng = event.latLng.lng;
        that.shopInfo.lat = event.latLng.lat;
        console.log(event.latLng.lng, event.latLng.lat)
        that.getAreaCode(event.latLng.lng, event.latLng.lat);
        if (markerLayer) {
          markerLayer.setGeometries([]);
        }
        markerLayer = new window.TMap.MultiMarker({
          map: that.map,
          styles: {
            // 点标记样式
            marker: new window.TMap.MarkerStyle({
              width: 20, // 样式宽
              height: 30, // 样式高
              anchor: {x: 10, y: 30}, // 描点位置
              src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png", // 标记路径
            }),
          },
        });
        // markerLayer.add({ position: event.latLng })
        markerLayer.add({
          id: that.shopInfo.lat,
          position: new window.TMap.LatLng(
            that.shopInfo.lat,
            that.shopInfo.lng
          ),
        });
      });
      if (lat && lng) {
        if (markerLayer) {
          markerLayer.setGeometries([]);
        }
        markerLayer = new window.TMap.MultiMarker({
          map: that.map,
          styles: {
            // 点标记样式
            marker: new window.TMap.MarkerStyle({
              width: 20, // 样式宽
              height: 30, // 样式高
              anchor: {x: 10, y: 30}, // 描点位置
              src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png", // 标记路径
            }),
          },
        });
        markerLayer.updateGeometries({
          id: lat,
          position: new window.TMap.LatLng(lat, lng),
        });
        that.map.setCenter(new window.TMap.LatLng(lat, lng));
      }
    },
    querySearch(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants
        : [];
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        );
      };
    },
    handleSelect(item) {
      const that = this;
      that.addressKeyword = ''
      that.getMarker(
        item.location.lng,
        item.location.lat
      );
    },
    mapInput(e) {
      const that = this;
      jsonp("https://apis.map.qq.com/ws/place/v1/suggestion", {
        output: "jsonp",
        keyword: e,
        region: "中国",
        key: "XIRBZ-TWKWD-AAX4Q-HEKFK-JGPIO-YNBTJ",
      })
        .then(function (response) {
          const arr = response.data;
          if (arr.length > 0) {
            arr.forEach((item) => {
              item.value = item.address + item.title;
            });
            that.restaurants = arr;
          }
        })

    },
    getAddressKeyword() {
      const that = this;

      jsonp("https://apis.map.qq.com/ws/geocoder/v1/?address=", {
        output: "jsonp",
        address: that.addressKeyword,
        region: "中国",
        key: "XIRBZ-TWKWD-AAX4Q-HEKFK-JGPIO-YNBTJ",
      })
        .then(function (response) {
          if (response.message === "查询无结果") {
            that.$message.warning("查询无结果");
            return false;
          }
          that.addressKeyword = ''
          that.shopInfo.lng = response.result.location.lng;
          that.shopInfo.lat = response.result.location.lat;

          that.getMarker(
            response.result.location.lng,
            response.result.location.lat
          );
        })

    },
    getAreaCode(lng, lat) {
      //这里可以直接this.$jsonp地址传入你的经纬度;
      jsonp("https://apis.map.qq.com/ws/geocoder/v1/?", {
        location: lat + "," + lng, // 经纬度
        key: "XIRBZ-TWKWD-AAX4Q-HEKFK-JGPIO-YNBTJ", //这里就是要开启那个service不然会报错让你开启
        output: "jsonp", // output必须jsonp   不然会超时
      }).then((res) => {
        //获取到的res 就是继续的地址的所有信息;
        this.shopInfo.addr = res.result.address;
        this.shopInfo.regionCode = res.result.ad_info.adcode;
        this.shopInfo.regionAddress = res.result.ad_info.name;
      });
    },

    getMarker(lng, lat) {
      let that = this;
      that.shopInfo.lng = lng;
      that.shopInfo.lat = lat;
      that.getAreaCode(lng, lat);
      if (markerLayer) {
        markerLayer.setGeometries([]);
      }

      markerLayer = new window.TMap.MultiMarker({
        map: that.map,
        styles: {
          // 点标记样式
          marker: new window.TMap.MarkerStyle({
            width: 20, // 样式宽
            height: 30, // 样式高
            anchor: {x: 10, y: 30}, // 描点位置
            src: "https://mapapi.qq.com/web/lbs/javascriptGL/demo/img/markerDefault.png", // 标记路径
          }),
        },
      });
      that.map.setCenter(
        new window.TMap.LatLng(that.shopInfo.lat, that.shopInfo.lng)
      );
      markerLayer.updateGeometries({
        id: that.shopInfo.lat,
        position: new window.TMap.LatLng(that.shopInfo.lat, that.shopInfo.lng),
      });
    },
    /***
     * 确认
     */
    confirm: function () {
      this.$emit("map-confirm", this.shopInfo);
    },
    /***
     * 取消
     */
    cancel: function () {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="scss" scoped>
.serachinput {
  width: 300px;
  box-sizing: border-box;
  padding: 9px;
  border: 1px solid #dddee1;
  line-height: 20px;
  font-size: 16px;
  height: 38px;
  color: #333;
  position: relative;
  border-radius: 4px;
  -webkit-box-shadow: #666 0px 0px 10px;
  -moz-box-shadow: #666 0px 0px 10px;
  box-shadow: #666 0px 0px 10px;
}

.custom-dialog {
  z-index: 9999 !important; /* Replace 9999 with the z-index value you want */
}

::v-deep .el-dialog__header {
  border-bottom: 0 !important;
}
</style>
