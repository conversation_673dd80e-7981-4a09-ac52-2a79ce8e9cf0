<template>
  <div>
    <el-select
      v-model="firstLevelvalue"
      placeholder="请选择省"
      size="small"
      @change="changeFirstCode"
    >
      <el-option
        v-for="item in firstLevelName"
        :key="item.code"
        :label="item.name"
        :value="item.code"
      >
      </el-option>
    </el-select>
    <span>&ensp;</span>
    <el-select
      v-model="secondLevelValue"
      placeholder="请选择市"
      size="small"
      @change="changeSecondCode"
    >
      <el-option
        v-for="item in secondLevelName"
        :key="item.code"
        :label="item.name"
        :value="item.code"
        :size="small"
      >
      </el-option>
    </el-select>
    <span>&ensp;</span>
    <el-select
      v-model="thirdLevelValue"
      placeholder="请选择区"
      size="small"
      @change="changeThirdCode"
    >
      <el-option
        v-for="item in thirdLevelName"
        :key="item.code"
        :label="item.name"
        :value="item.code"
        :size="small"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import { getProvinceAddress } from "@/api/common/address";
export default {
  props: {
    currentDetailRow: {
      //回显数据
      type: Object,
      default: () => {
        return {};
      }
    },
    currentFormStatus: {
      //是否需要回显 1.是
      type: Number,
      default: () => {
        return 1;
      }
    },
  },
  data() {
    return {
      firstLevelName: [],
      firstLevelvalue: "",
      secondLevelName: [],
      secondLevelValue: "",
      thirdLevelName: [],
      thirdLevelValue: "",
      addressValue: []
    };
  },
  created() {
    this.getAddress();
    if(this.currentDetailRow[0] != undefined && this.currentDetailRow[0] != ""){
      setTimeout(()=>{
        this.setAllAddress(this.currentDetailRow[0],this.currentDetailRow[1],this.currentDetailRow[2]);
      },100);
    }
  },
  methods: {
    async getAddress() {
      let result = await getProvinceAddress("86");
      this.firstLevelName = result.data.data;
    },

    async changeFirstCode() {
      this.secondLevelValue = "";
      this.thirdLevelValue = "";
      this.addressValue = [];
      let result = await getProvinceAddress(this.firstLevelvalue);
      this.secondLevelName = result.data.data;
    },
    async changeSecondCode() {
      this.thirdLevelValue = "";
      this.addressValue = [];
      let result = await getProvinceAddress(this.secondLevelValue);
      this.thirdLevelName = result.data.data;
    },
    changeThirdCode() {
      this.addressValue = [];
      this.addressValue.push(
        this.firstLevelvalue,
        this.secondLevelValue,
        this.thirdLevelValue
      );
      console.log(this.addressValue);
      this.$emit("sendProvinceList", this.addressValue);
    },
    getAllAddress(){
      return this.addressValue;
    },
    async setAllAddress(provinceCode,cityCode,areaCode){
      let result1 = await getProvinceAddress("86");
       this.firstLevelName = result1.data.data;
      this.firstLevelvalue = provinceCode;
       let result2 = await getProvinceAddress(this.firstLevelvalue);
      this.secondLevelName = result2.data.data;
      this.secondLevelValue = cityCode;
      let result3 = await getProvinceAddress(this.secondLevelValue);
      this.thirdLevelName = result3.data.data;
      this.thirdLevelValue = areaCode;
      //给标签赋值
      this.addressValue = [];
      this.addressValue.push(
        this.firstLevelvalue,
        this.secondLevelValue,
        this.thirdLevelValue
      );
    }

    // setAllAddress(provinceCode,cityCode,areaCode){
    //   this.firstLevelvalue = provinceCode;
    //   console.log(this.firstLevelvalue);
    //   this.changeFirstCode();
    //   setTimeout(()=>{
    //     this.secondLevelValue = cityCode;
    //     this.changeSecondCode();
    //     setTimeout(()=>{
    //       this.thirdLevelValue = areaCode;
    //       this.changeThirdCode();
    //     },100);
    //   },100);
    // },
  }
};
</script>
