<!--
  @name: 下拉选择
  @description:带checkbox多选组件
  @author: 卢凤淦
  @date: 2024-07-16
-->
<template>
  <el-select v-model="selectValue" :placeholder="placeholder" :multiple="isMultiple" :filterable="isFilterable"
    :isGrouping="isGrouping" :collapse-tags="true" :size="size" :filter-method="filterOptions" :clearable="isClearable"
    @change="selectChange" @clear="selectClear">
    <div style="padding: 5px 20px" v-if="isMultiple">
      <el-checkbox v-model="checked" @change="selectAll()">全选</el-checkbox>
    </div>
    <!--  isGrouping是否开启分组,默认不开启(false)  -->
    <div v-if="isGrouping">

      <el-option-group v-for="group in filterOption" :key="group.key" :label="group.label">
        <el-option v-for="item in group.options" :key="item[appointValue]" :label="item[appointValue]"
          :value="item[appointKey]">
          <el-checkbox v-if="isMultiple && isShowId" style="pointer-events: none;padding: 0 20px"
            :value="selectValue ? selectValue.includes(item[appointKey]) : false">{{ "【" + item[appointKey] + "】" +
              item[appointValue] }}</el-checkbox>
          <el-checkbox v-if="isMultiple && !isShowId" style="pointer-events: none;padding: 0 20px"
            :value="selectValue ? selectValue.includes(item[appointKey]) : false">{{ item[appointValue] }}</el-checkbox>
        </el-option>
      </el-option-group>
    </div>
    <!--  isGrouping是否开启分组,默认不开启(false)  -->
    <div v-else>
      <el-option v-for="item in filterOption" :key="item[appointKey]" :label="item[appointValue]"
        :value="item[appointKey]">
        <el-checkbox v-if="isMultiple && isShowId" style="pointer-events: none;padding: 0 20px"
          :value="selectValue ? selectValue.includes(item[appointKey]) : false">
          <el-tag effect="dark" :size="size" v-if="appointTag && item[appointTag]">{{ item[appointTag] ===
            1 ? '路外' : '路内' }}</el-tag>
          {{ appointTag && item[appointTag] ? item[appointValue] : '【' + item[appointKey] + '】' + item[appointValue] }}
        </el-checkbox>
        <el-checkbox v-if="isMultiple && !isShowId" style="pointer-events: none;padding: 0 20px"
          :value="selectValue ? selectValue.includes(item[appointKey]) : false">{{ item[appointValue] }}</el-checkbox>
      </el-option>
    </div>

  </el-select>
</template>

<script>
/**
 * @name: 下拉选择
 * @description:带checkbox多选组件
 * @author: 卢凤淦
 * @date: 2024-07-16
 */
export default {
  name: 'checkboxSelect',
  data() {
    return {
      // 是否选择
      checked: false,
      selectValue: undefined,
      filterOption: [] // 搜索数据
    }
  },
  props: {
    // 下拉数据
    optionList: {
      type: Array,
      default: () => [],
    },
    // 是否开启分组,默认不开启(false)
    isGrouping: {
      type: Boolean,
      default: false,
    },
    // 是否开启多选,默认不开启(false)
    isMultiple: {
      type: Boolean,
      default: false,
    },
    // 是否开启搜索,默认不开启(false)
    isFilterable: {
      type: Boolean,
      default: false,
    },
    // 是否显示条目ID,开启钱确认是否有ID字段,默认不开启(false)
    isShowId: {
      type: Boolean,
      default: false,
    },
    // 指定tag todo 跟停车业务相关，后面抽离至车场选择组件
    appointTag: {
      type: String,
      default: '',
    },
    // 指定key,默认值:id
    appointKey: {
      type: String,
      default: 'id',
    },
    // 指定value,默认值:name
    appointValue: {
      type: String,
      default: 'name',
    },
    // medium/small/mini
    size: {
      type: String,
      default: 'small',
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    isClearable: {
      type: Boolean,
      default: false,
    },
    // 选中值
    value: undefined
  },
  watch: {
    optionList: {
      immediate: true,
      handler: function (val) {
        this.filterOption = val
        // if (this.isGrouping) {
        //   let totalOptionsCount = this.filterOption.reduce((accumulator, currentOptionList) => {
        //     return accumulator + currentOptionList.options.length;
        //   }, 0);
        //   this.checked = this.selectValue.length === totalOptionsCount
        // } else {
        //   this.checked = this.selectValue.length === this.optionList.length
        // }
      }
    },
    value: {
      immediate: true,
      handler: function (val) {
        this.filterOption = this.optionList
        if (this.isMultiple) {
          this.$nextTick(() => {
            if (val && val.length > 0) {
              this.selectValue = val.split(',')
              if (this.isGrouping) {
                let totalOptionsCount = this.optionList.reduce((accumulator, currentOptionList) => {
                  return accumulator + currentOptionList.options.length;
                }, 0);
                this.checked = this.selectValue.length === totalOptionsCount
              } else {
                this.checked = this.selectValue.length === this.optionList.length
              }
            } else {
              this.checked = false
              this.selectValue = []
            }
          })
        } else {
          this.selectValue = val
        }
      }
    },
  },
  methods: {
    /**
     * @function: 功能描述
     * @param val select选中值
    */
    selectChange(val) {
      if (!this.isMultiple) {
        let item = '';
        if (this.isGrouping) {
          this.optionList.forEach(fitem => item = fitem.options.find(items => items[this.appointKey] === val))
        } else {
          item = this.optionList.find(item => item[this.appointKey] === val)
        }
        this.$emit('update:value', val)
        this.$emit('selectCallback', item)
      } else {
        const selectedItems = this.optionList.filter(item =>
          this.selectValue.includes(item[this.appointKey])
        )
        this.$emit('update:value', this.selectValue.join(','))
        this.$emit('selectCallback', selectedItems, this.selectValue.join(','))
      }
    },
    selectClear() {
      this.$emit('update:value', '')
      this.$emit('selectCallback', [])
    },
    /**
     * @function: 全选事件,回调selectCallback(item)
     */
    selectAll() {
      if (this.checked) {
        this.selectValue = []
        let items = []

        if (this.isGrouping) {
          this.filterOption.forEach(group => {
            group.options.forEach(optionsItem => {
              items.push(optionsItem)
              this.selectValue.push(optionsItem[this.appointKey])
            })
          })
        } else {
          this.filterOption.forEach(item => {
            items.push(item)
            this.selectValue.push(item[this.appointKey])
          })
        }

        this.$emit('update:value', this.selectValue.join(','))
        this.$emit('selectCallback', items)
      } else {
        this.selectValue = []
        this.$emit('update:value', '')
        this.$emit('selectCallback', [])
      }
    },
    /**
     * @function: 数据重制
     */
    clearData() {
      this.selectValue = []
      this.$emit('update:value', '')
      this.$emit('selectCallback', [])
    },
    filterOptions(searchKey) {
      if (searchKey) {
        if (this.isGrouping) {
          this.filterOption = this.optionList.map(group => ({
            ...group,
            options: group.options.filter(option =>
              option[this.appointValue].toLowerCase().includes(searchKey.toLowerCase())
            )
          }))
        } else {
          this.filterOption = this.optionList.filter(item => {
            return item[this.appointValue].toLowerCase().includes(searchKey.toLowerCase())
          })
        }
      } else {
        this.filterOption = this.optionList
      }
    }
  }
}
</script>
