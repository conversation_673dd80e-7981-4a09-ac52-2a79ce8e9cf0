<!--
  @name: 业务增强版下拉选择框
  @description:业务增强版下拉选择框
  @author: 卢凤淦
  @date: 2024-07-22
-->
<template>
  <checkboxSelect ref="checkboxSelect" style="width: 100%" :value.sync="value" :optionList="parklotOption" :is-show-id="option.isShowId" :is-multiple="option.isMultiple" @selectCallback="selectCallback"></checkboxSelect>
</template>
<script>
import CheckboxSelect from "@/components/checkboxSelect/checkboxSelect.vue";

export default {
  components: {CheckboxSelect},
  data(){
    return{
      parklotOption:[],
      value:'',
    }
  },
  props:{
    // 业务配置
    option:{
      // 是否开启分组,默认不开启(false)
      isGrouping:{
        type:Boolean,
        default:false,
      },
      // 是否开启多选,默认不开启(false)
      isMultiple:{
        type:Boolean,
        default:false,
      },
      // 是否开启搜索,默认不开启(false)
      isFilterable:{
        type:Boolean,
        default:false,
      },
      // 是否显示条目ID,开启钱确认是否有ID字段,默认不开启(false)
      isShowId:{
        type:Boolean,
        default:false,
      },
      // 指定key,默认值:id
      appointKey:{
        type:String,
        default:'id',
      },
      // 指定value,默认值:name
      appointValue:{
        type:String,
        default:'name',
      },
      // 控件大小medium/small/mini
      size:{
        type:String,
        default:'small',
      },
      // 空值提示语
      placeholder:{
        type:String,
        default:'请选择',
      },
      // 远程数据
      dicUrl:{
        type:String,
        default:'',
      },
      // 本地数据
      dicData:{
        type:String,
        default:[],
      },
    }
  },
  watch:{
    option: {
      handler: function (newUser, oldUser) {

      },
      deep: true // 设置 deep 选项为 true，实现深度监听
    }
  },
  methods:{
    selectCallback(){

    },
    clearData(){
      this.$refs.checkboxSelect.clearData()
    }
  }
}
</script>
