<template>
  <div class="search-content">
    <el-radio-group size="medium" v-model="radioValue" @change="changeRadio">
      <el-radio-button v-if="type.includes('day')" :label="1">日</el-radio-button>
      <el-radio-button v-if="type.includes('week')" :label="2">周</el-radio-button>
      <el-radio-button v-if="type.includes('month')" :label="3">月</el-radio-button>
      <el-radio-button v-if="type.includes('year')" :label="4">年</el-radio-button>
      <el-radio-button v-if="type.includes('custom')" :label="5">自定义</el-radio-button>
    </el-radio-group>
    <div class="timebox">
      <el-date-picker v-show="radioValue == 1"  v-model="dayData" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" @change="getData(dayData)" size="medium" start-placeholder="请选择开始日期" end-placeholder="请选择结束日期"></el-date-picker>
      <el-date-picker v-show="radioValue == 2"  v-model="weekData" type="week" format="yyyy 第 WW 周" @change="getData(weekData)" size="medium" placeholder="选择周"></el-date-picker>
      <el-date-picker v-show="radioValue == 3"  v-model="monthData" type="monthrange" format="yyyy-MM" value-format="yyyy-MM" @change="getData(monthData)" size="medium" start-placeholder="请选择开始月份" end-placeholder="请选择结束月份"></el-date-picker>
      <el-date-picker v-show="radioValue == 4"  v-model="yearData" type="year" format="yyyy" value-format="yyyy" @change="getData(yearData)" size="medium" placeholder="选择年"></el-date-picker>
      <el-date-picker v-show="radioValue == 5"  v-model="customData" :default-time="['00:00:00','23:59:59']" type="datetimerange" range-separator="至" @change="getData(customData)" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" start-placeholder="开始日期" size="medium" end-placeholder="结束日期"></el-date-picker>
    </div>

  </div>
</template>

<script>
import { dateFormat } from '@/util/date'
export default {
  props: {
    value: {
      type: String,
      default: () => {
        return ''
      }
    },
    type: {
      type: Array,
      default: () => {
        return ['day', 'week', 'month', 'year', 'custom']
      }
    },
    defaultType: {
      type: String,
      default: '',
    },
    defaultDate: {
      type: String,
      default: dateFormat(new Date(), 'yyyy-MM-dd')
    }
  },
  data() {
    return {
      radioValue: null,
      dayData: [],
      weekData: '',
      monthData: [],
      yearData: '',
      customData: '',
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    //初始化
    initData() {
      this.radioValue = this.defaultType
      switch (this.radioValue) {
        case 1:
          this.dayData = [this.defaultDate, this.defaultDate]
          break
        case 2:
          this.weekData = this.defaultDate
          break
        case 3:
          this.monthData = [this.defaultDate, this.defaultDate]
          break
        case 4:
          this.yearData = this.defaultDate
          break
        case 5:
          this.customData = this.defaultDate
          break
      }
      this.getData(this.dayData);
    },
    //重置
    reset() {
      this.radioValue = this.defaultType
      this.initData();
    },
    changeRadio(val) {
      this.$emit('changeRadio', val);
      //切换类型时需要更新值
      switch (val) {
        case 1:
          this.getData(this.dayData);
          break
        case 2:
          this.getData(this.weekData);
          break
        case 3:
          this.getData(this.monthData);
          break
        case 4:
          this.getData(this.yearData);
          break
        case 5:
          this.getData(this.customData);
          break
      }
    },
    //获得时间格式化并返回给父组件
    getData(v) {
      let startDate = ''
      let endDate = ''
      if (this.radioValue === 1) {
        startDate = v[0] + ' 00:00:00'
        endDate = v[1] + ' 23:59:59'
      }
      // 如果是周选择器且传入了 Date 对象，需要格式化
      if (this.radioValue === 2) {
        startDate = v.startOf('week') + ' 00:00:00'
        endDate = v.endOf('week') + ' 23:59:59'
      }
      if (this.radioValue === 3) {
        //选择月需要处理2月问题，2月没有31天
        if (v.length > 0) {
          let year = v[1].split('-')[0]
          let month = v[1].split('-')[1]
          let days = new Date(year, month, 0).getDate()
          startDate = v[0] + '-01 00:00:00'
          endDate = v[1] + '-' + days + ' 23:59:59'
        }
      }
      if (this.radioValue === 4) {
        startDate = v + '-01-01 00:00:00'
        endDate = v + '-12-31 23:59:59'
      }
      if (this.radioValue === 5) {
        startDate = v[0]
        endDate = v[1]
      }
      this.$emit('input', { startDate, endDate, cycleType: this.radioValue })
    },
  }
}
</script>

<style lang="scss" scoped>
.search-content {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	background-color: #fff;
	// padding-bottom: 10px;
	border-radius: 10px;
	gap: 15px;
	.timebox {
		display: inline-block;

		/deep/ .el-input__suffix {
			display: none;
		}
	}
}
</style>