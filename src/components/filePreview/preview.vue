<template>
  <div class="fileContainer">
    <div class="stepPersonDetail" >
      <div class="stepPersonDetailC">
        <div class="detailCAttach">
          <div class="attachImg">
            <div
              class="attachImgList"
              v-for="(val, index) in showImgs"
              v-bind:key="index"
            >
              <div class="attachImgInner">
                <el-image
                  style="width: 50px; height: 50px; margin: 5px"
                  :src="val"
                  :preview-src-list="showImgs"
                >
                </el-image>
              </div>
            </div>
          </div>
          <div class="attachFile">
            <div
              class="attachFileList"
              v-for="(val, index) in showFiles"
              v-bind:key="index"
            >
              <div class="fileListInner">
                <i class="el-icon-document attachFileIcon"></i>
                <a class="attachFileText" :href="val">{{
                  val.slice(val.lastIndexOf("/") + 1)
                }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

export default {
  name: "preview",
  components: {
  },
  props: {
    showList: {
      type: Boolean,
      require: false,
      default: true,
    },
    files: {
      type: Array,
      require: false,
    },
  },
  created() {
  },
  data() {
    return {
      showImgs: [],
      showFiles: [],
      imgFileType: [
        // 用于展示错误提示信息:匹配 .jpg jpg jpeg .jpeg image/jpg image/jpeg
        ["jpeg", /(^image\/jpe?g$)|^\.?jpe?g$/],
        ["png", /(^image\/png$)|^\.?png$/],
        ["gif", /(^image\/gif$)|^\.?gif$/],
      ],


   
      filterFiles: [],
      filterImgs: [],
    };
  },
  watch: {
    files(val) {
        this.showImgs = []
        this.showFiles = []
        if(val){
            this.setInboundFiles();
        }
        
    },
  },
  computed: {
  
  },
  methods: {
    // 通过files.sync传进来的文件URL列表
    setInboundFiles() {
      for (let i in this.files) {
        let url_ = this.files[i];
        this.updateUploadedFiles(
          url_.slice(url_.lastIndexOf("/") + 1),
          url_,
          true
        );
      }
    },
    /**
     * 如果reverse为true，表示上传的文件URL是从外部files参数传入进来的，用于回恢复当前上传状态，
     * 这时不应该修改files对象本身
     */
    updateUploadedFiles(name, url, reverse) {
      if (this.isImg_(name)) {
        this.showImgs.push(url)
      } else {
        this.showFiles.push(url)
      }
      
    },
    isImg_(name) {
      name = name.slice(name.lastIndexOf("."));
      name = name.toLowerCase();
      for (let i in this.imgFileType) {
        if (this.imgFileType[i][1].test(name)) {
          return true;
        }
      }
      return false;
    },



  },
};
</script>
<style lang="scss" scoped>

  .stepPersonDetail {
    display: flex;
    flex-direction: column;

    .stepPersonDetailC {
      color: #303133;
      margin: 0.3rem 0;
      .detailCKey {
        margin: 0 0.7rem 0 0;
        float: left;
        overflow: hidden;
      }
      .detailCvalue,
      .detailCAttach {
        overflow: hidden;
      }
      .detailCAttach {
        .attachFileIcon {
          cursor: pointer;
          margin: 0 5px;
        }
        .attachFileText {
          text-overflow: ellipsis;
          box-sizing: border-box;
          white-space: nowrap;
          overflow: hidden;
          padding: 0 27px 0 0;
        }
        .attachFileIconClose {
          position: absolute;
          right: 0;
        }
        .attachImg {
          &::after {
            content: " ";
            display: table;
            clear: both;
          }
          .attachImgInner {
            position: relative;
          }
          .attachImgList {
            float: left;
          }
          .attachImgInnerClose {
            position: absolute;
            right: -3px;
            top: 3px;
            background-color: #fff;
            font-size: 1.2rem;
            border-radius: 50%;
          }
        }
        .attachFile {
          width: 100%;
          display: block;
          &::after {
            clear: both;
            content: " ";
            display: table;
          }
        }
        .attachFileList {
          font-size: 1rem;
          display: flex;
          // flex-direction: column;
          // align-items: center;
          // align-items: flex-start;
          float: left;
          box-sizing: border-box;
          position: relative;
          margin: 3px 0;
          width: 50%;
          .fileListInner {
            border: dashed 1px #ccc;
            position: relative;
            width: 90%;
            display: flex;
            flex-direction: row;
            align-items: center;
          }
        }
        // .attachImg {
        // }
        // .attachImgList {
        // }
      }
    }
  }
</style>
