<template>
  <!--  帧图片组件 -->
  <div ref="myDiv" class="frame-image-box" v-show="isShow" :loading="loading">
    <el-card class="frame-image-card">
      <div slot="header">
        <div style="display: flex;">
          <span class="box_title" style="flex: 1;" @click="close">
            <i class="el-icon-arrow-left"></i>
            <span>返回</span>
          </span>

          <div style="font-size: 14px;">
            <span>{{ parkingPlaceIteratorData.currParklotName }}-{{ parkingPlaceIteratorData.currPlace.placeCode }}
            </span>
            <span @click="clickPlace('up')" style="color: #409EFF;margin-left: 10px;cursor: pointer;"><i
                class="el-icon-top"></i>上一个</span>
            <span style="margin-left: 10px;">|</span>
            <span @click="clickPlace('down')" style="color: #409EFF;margin-left: 10px;cursor: pointer;"><i
                class="el-icon-bottom"></i>下一个</span>
          </div>
        </div>

      </div>
      <div style="display: flex;flex-direction: column;height:100%;overflow: auto">
        <div style="height: 100vh;position: relative;display: flex;align-items: center;padding:0px;">

          <div style="flex:1;display: flex;align-items: center;justify-content: center;position: relative;">
            <div style="text-align: center;width: 100vh;display: flex;align-items: center;">
              <div class="left-box" @click="leftClick()">
                <i class="el-icon-arrow-left" style="font-size: 40px;font-weight: 800"></i>
              </div>
              <div v-if="imgLoading || url">
                <el-image class="image-container" style="width: 80%" :preview-src-list="[url]" :src="url ? url : ''"
                  fit="contain">
                  <div v-if="imgLoading" slot="error" class="image-slot">
                    加载中<span class="dot">...</span>
                  </div>
                </el-image>
                <div style="margin-top: 10px;color: #909399;font-size: 14px">
                  <span style="margin-left: 20px;">车位编号：</span>
                  <span style="color: #409eff;font-weight: 600;" v-if="payCode"> {{
                    parkingPlaceIteratorData.currPlace.placeCode }}</span>

                  <span style="margin-left: 20px;">抓拍时间：</span>
                  <span style="font-weight: 600;color: #333333;">{{ setContent('triggerTime') }}</span>

                  <span style="margin-left: 20px;">停车状态：</span>
                  <!-- 停车状态：未知、占用、空闲 根据状态显示不同的颜色 -->
                  <!-- <span style="font-weight: 600;color: #333333;">{{ setContent('parkingStatus') }}</span> -->
                  <span v-if="setContent('parkingStatus') == '未知'" style="color: #909399;font-weight: 600;">未知</span>
                  <span v-if="setContent('parkingStatus') == '占用'" style="color: #f56c6c;font-weight: 600;">占用</span>
                  <span v-if="setContent('parkingStatus') == '空闲'" style="color: #67c23a;font-weight: 600;">空闲</span>

                  <span style="margin-left: 20px;">抓拍车牌：</span>
                  <span style="font-weight: 600;color: #333333;"> {{ setContent('plate') }}</span>

                  <span style="margin-left: 20px;">秒图类型：</span>
                  <span style="font-weight: 600;color: #333333;"> {{ setContent('type') }}</span>
                </div>
              </div>
              <el-empty v-else :image-size="400" style="flex:1;"></el-empty>
              <div class="right-box" @click="rightClick()">
                <i class="el-icon-arrow-right" style="font-size: 40px;font-weight: 800"></i>
              </div>
            </div>
          </div>
          <div style="position: absolute;right:5px;top: 3px;cursor: pointer;z-index: 1000;"
            @click="isShowTimeLien = !isShowTimeLien">
            <i class="el-icon-s-unfold" style="font-size: 30px;color:#4290f7"></i>
          </div>
          <transition name="slide">
            <div v-show="isShowTimeLien"
              style="height: 100%;width: 352px;overflow-y: hidden;box-shadow:0 0px 12px 0 rgba(0,0,0,.1);">
              <ifrmeTimeLien ref="ifrmeTimeLien" @parkingRecord="parkingRecord" :startDate="startDate"
                :endDate="endDate" :itemPlaceId="placeId" />
            </div>
          </transition>

        </div>

        <div style="flex: 1; padding-bottom: 15px;">
          <div style="text-align: center;
              background: #fafafa;
              border-top: 1px solid #e3e3e3;
              border-bottom: 1px solid #e3e3e3;
              height: 46px;
              overflow: hidden;
              display:flex;
              justify-content: center;
              align-items: center">
            <div style="display: flex">
              <el-button size="small" style="margin-right: 10px" @click="timeUpdate('left', 4)"> <i
                  class="el-icon-caret-left"></i> 4h</el-button>
              <el-button size="small" style="margin-right: 10px" @click="timeUpdate('left', 2)"> <i
                  class="el-icon-caret-left"></i> 2h</el-button>
              <el-button size="small" style="margin-right: 10px" @click="timeUpdate('left', 1)"> <i
                  class="el-icon-caret-left"></i> 1h</el-button>
            </div>

            <el-date-picker v-model="value1" type="datetimerange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" size="small"
              style="margin-right: 20px" @change="change">
            </el-date-picker>

            <div style="display: flex">
              <el-button size="small" style="margin-right: 10px" @click="timeUpdate('right', 1)"> <i
                  class="el-icon-caret-right"></i> 1h</el-button>
              <el-button size="small" style="margin-right: 10px" @click="timeUpdate('right', 2)"> <i
                  class="el-icon-caret-right"></i> 2h</el-button>
              <el-button size="small" style="margin-right: 10px" @click="timeUpdate('right', 4)"> <i
                  class="el-icon-caret-right"></i> 4h</el-button>

              <el-button type="text" size="small" style="margin-right: 10px" @click="timeUpdate('refresh', 0)"> <i
                  class="el-icon-refresh-right"></i> 最新秒图</el-button>
              <!-- 把升、序这里改为按钮,样式按照el-radio-group -->

              <div class="my-radio-group left_radio_group" :class="timeSort == 'ascs' ? 'my-radio-group-selected' : ''"
                @click="changeTimeSort('ascs')">升序</div>
              <div class="my-radio-group right_radio_group" :class="timeSort == 'descs' ? 'my-radio-group-selected' : ''"
                @click="changeTimeSort('descs')">降序</div>

              <!-- <el-radio-group v-model="timeSort" size="small" @change="changeTimeSort">
                <el-radio-button label="ascs" >升序</el-radio-button>
                <el-radio-button label="desc" >降序</el-radio-button>
              </el-radio-group> -->
            </div>
            <!--            <el-input v-model="place" placeholder="请输入车牌查询" style="width: 200px"  size="small"></el-input>-->
            <!--            <i v-if="setIntervalObj" @click="clearIntervalMethods()" class="el-icon-video-pause" style="font-size: 30px;"></i>-->
            <!--            <i v-else @click="framePlaye()" class="el-icon-video-play" style="font-size: 30px;"></i>-->
            <div style="margin-left: 10px;color: #909399;font-size: 14px">
              <span>{{ page.current }}/{{ page.pages }}页</span>
            </div>
          </div>

          <div style="display: flex;justify-content: space-between">
            <div class="page-turning" @click="pageTurningLeft()">
              <i class="el-icon-arrow-left" style="font-size: 36px;font-weight: 800;"></i>
            </div>

            <div class="image-Box"
              style="flex: 1; padding: 15px 0;  display:flex;overflow-x: auto;white-space: nowrap;margin: 0 10px;min-height: 130px;">
              <swiper :options="swiperOptions" ref="swiper">
                <swiper-slide v-for="(itme, index) in visibleImglist" :key="index"
                  style="display: flex;align-items: center;justify-content: center">
                  <el-image style="width:100px;height:70px;cursor: pointer;" :src="itme.imageUrl" fit="fill"
                    @click="urlClick(itme, index)">
                  </el-image>
                </swiper-slide>
                <!-- swiper默认上下页，需要做隐藏处理 -->
                <div class="swiper-button-prev" style="width: 1px;height: 1px;"></div>
                <div class="swiper-button-next" style="width: 1px;height: 1px;"></div>
              </swiper>
            </div>

            <div class="page-turning" @click="pageTurningRight()">
              <i class="el-icon-arrow-right" style="font-size: 36px;font-weight: 800;"></i>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>


<script>
import { getTimeImage, getParkingPlaceIterator } from '@/api/public';
import { dateFormat } from "@/util/date";
import ifrmeTimeLien from './ifrmeTimeLien.vue'
export default {
  components: {
    ifrmeTimeLien
  },
  data() {
    return {
      placeId: '',
      parkingPlaceIteratorData: {
        currPlace: {}
      },
      timeSort: "desc",
      isShowTimeLien: true,
      visibleImgPage: 1, // 可见区域内的图片页码
      visibleImglist: [], // 可见区域内的图片列表
      swiperOptions: {
        cssMode: true,
        autoplay: false,
        loop: false,
        slidesPerView: 'auto',
        centeredSlides: true,
        spaceBetween: 10,
        allowTouchMove: false,
        touchMoveStopPropagation: true,
        touchReleaseOnEdges: true,
        pagination: {
          el: '.swiper-pagination',
          click: true
        },
        navigation: {
          nextEl: 'swiper-button-prev',
          prevEl: 'swiper-button-next',
          hideOnClick: false,
        },
        on: {
          slideChangeTransitionEnd: () => {
            // 滑动结束后，获取当前活动的滑块的下标
            var realIndex = this.swiper.realIndex; // 注意这里的 'this' 指向 Swiper 实例
            this.urlIndex = realIndex
          }
        }
      },
      value1: [],
      page: {
        current: 1,
        size: 20,
        pages: 0,
      },
      total: 0,
      loading: false,
      urlIndex: 0,
      setIntervalObj: '',
      fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
      url: '',
      url2: [],
      imgLoading: false
    }
  },
  props: {
    itemPlaceId: '',
    startDate: '',
    endDate: '',
    payCode: '',
    isShow: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    isShow(news, olds) {
      if (news) {
        console.log(this.urlIndex)
        console.log(this.startDate, this.endDate)
        this.urlIndex = 0
        this.url = this.visibleImglist[0]
        this.getParkingPlaceIteratorData()
        this.$nextTick(() => {
          document.addEventListener('keydown', this.eventListener);
        })
      }
    },
  },
  computed: {
    swiper() {
      return this.$refs.swiper.swiper
    }
  },
  methods: {
    getParkingPlaceIteratorData() {
      this.placeId = this.placeId ? this.placeId : this.itemPlaceId
      getParkingPlaceIterator(this.placeId).then(res => {
        this.parkingPlaceIteratorData = res.data.data
        this.getTimeImageData()
      })
    },
    clickPlace(type) {
      if (type == 'up') {
        if (!this.parkingPlaceIteratorData.lastPlace || !this.parkingPlaceIteratorData.lastPlace.placeId) {
          this.$message.warning('已经是第一个了')
          return
        }
        this.placeId = this.parkingPlaceIteratorData.lastPlace.placeId
      } else {
        if (!this.parkingPlaceIteratorData.nextPlace || !this.parkingPlaceIteratorData.nextPlace.placeId) {
          this.$message.warning('已经是最后一个了')
          return
        }
        this.placeId = this.parkingPlaceIteratorData.nextPlace.placeId
      }
      this.urlIndex = 0
      this.url = this.visibleImglist[0]
      this.itemPlaceId = this.parkingPlaceIteratorData.currPlace.placeId
      this.getParkingPlaceIteratorData()
    },
    changeTimeSort(val) {
      this.timeSort = val
      this.timeUpdate('refresh', 0)
      this.$refs.ifrmeTimeLien.changeSort(this.timeSort);
    },
    parkingRecord(orderId) {
      this.$emit('parkingRecord', orderId)
    },

    eventListener(event) {
      console.log('keydown', event.key)
      if (event.key == 'ArrowLeft') {
        this.leftClick()
      } else if (event.key == 'ArrowRight') {
        this.rightClick()
      }
    },
    timeUpdate(type, val) {
      if (type === 'left') {
        this.value1[0] = dateFormat(new Date(this.startDate).setHours(new Date(this.startDate).getHours() - val))
        this.startDate = dateFormat(new Date(this.startDate).setHours(new Date(this.startDate).getHours() - val))
        // this.value1[1] = dateFormat(new Date(this.endDate).setHours(new Date(this.endDate).getHours()-val))
        // this.endDate = dateFormat(new Date(this.endDate).setHours(new Date(this.endDate).getHours()-val))
        this.$set(this.value1, this.value1[0], dateFormat(new Date(this.startDate).setHours(new Date(this.startDate).getHours() - val)))
        // this.$set(this.value1,this.value1[1],dateFormat(new Date(this.endDate).setHours(new Date(this.endDate).getHours()-val)))
      } else if (type === 'right') {
        // this.value1[0] = dateFormat(new Date(this.startDate).setHours(new Date(this.startDate).getHours()+val))
        // this.startDate = dateFormat(new Date(this.startDate).setHours(new Date(this.startDate).getHours()+val))
        this.value1[1] = dateFormat(new Date(this.endDate).setHours(new Date(this.endDate).getHours() + val))
        this.endDate = dateFormat(new Date(this.endDate).setHours(new Date(this.endDate).getHours() + val))
        // this.$set(this.value1,this.value1[0],dateFormat(new Date(this.startDate).setHours(new Date(this.startDate).getHours()+val)))
        this.$set(this.value1, this.value1[1], dateFormat(new Date(this.endDate).setHours(new Date(this.endDate).getHours() + val)))
      } else if (type === 'refresh') {
        this.value1[1] = dateFormat(new Date())
        this.endDate = dateFormat(new Date())
        this.$set(this.value1, this.value1[1], dateFormat(new Date()))
      }
      this.url2 = []
      this.visibleImglist = []
      this.url = ''
      this.urlIndex = 0
      this.page.current = 1
      this.visibleImgPage = 1
      this.getTimeImageData()
    },
    pageTurningLeft() {
      this.visibleImgPage = this.visibleImgPage - 1 === 0 ? 1 : this.visibleImgPage - 1
      if (this.page.current === 1) {
        this.$message.warning('已经是第一页了')
        return
      }
      this.page.current--
      this.getTimeImageData()
    },
    pageTurningRight() {
      if (this.visibleImgPage === this.page.current) {
        if (this.page.current === this.page.pages) {
          this.$message.warning('没有更多数据了');
          return
        }
        this.page.current++
        this.visibleImgPage++
        this.getTimeImageData()
      } else {
        this.visibleImgPage++
        this.changeVisibleImg()
      }

    },
    setContent(type) {
      if (this.visibleImglist.length === 0) return '';
      const state = ['未知', '占用', '空闲']
      let content = '';
      switch (type) {
        case 'parkingStatus':
          // console.log('parkingStatus', this.visibleImglist[this.urlIndex].parkingState)
          content = state[this.visibleImglist[this.urlIndex].parkingState + 1] || '';
          break;
        case 'triggerTime':
          content = this.visibleImglist[this.urlIndex].captureTime || '';
          break;
        // eslint-disable-next-line no-case-declarations
        case 'type':
          const typeList = {
            0: "未知抓拍",
            1: "入场抓拍",
            2: "出场抓拍",
            3: "定时抓拍",
            4: "手动抓拍",
            5: "遮挡抓拍",
          };
          content = this.visibleImglist[this.urlIndex].type ? typeList[this.visibleImglist[this.urlIndex].type] : '';
          break;
        // eslint-disable-next-line no-case-declarations
        default:
          const plateInfo = this.visibleImglist[this.urlIndex];
          content = plateInfo ? `(${plateInfo.plateColorCnName})${plateInfo.plate}` : '';
      }
      return content
    },
    change(row) {
      this.url2 = []
      this.visibleImglist = []
      this.url = ''
      if (row.length >= 2) {
        this.startDate = row[0]
        this.endDate = row[1]
      }
      this.page.current = 1
      this.visibleImgPage = 1
      this.getTimeImageData()
    },
    leftClick() {
      if (this.urlIndex === 0) {
        if (this.visibleImgPage - 1 === 0) {
          this.$message.warning('已经是第一张了')
          return
        }
        this.visibleImgPage = this.visibleImgPage - 1 === 0 ? 1 : this.visibleImgPage - 1
        this.page.current--
        this.getTimeImageData()
      } else {
        this.urlIndex--
        this.updateImageUrl(this.visibleImglist[this.urlIndex].imageUrl, this.urlIndex)
      }
    },
    rightClick() {
      // 判断是否是当前列表最后一张图片
      if ((this.urlIndex + 1) === this.visibleImglist.length) {

        // 如果是当前列表最后一张图片，并且当前图片列表显示的分页visibleImgPage 已经是接口请求数据的最新分页current，则加载下一页数据
        if (this.visibleImgPage === this.page.current) {
          if (this.page.current + 1 > this.page.pages) {
            this.$message.warning('没有更多数据了');
            return
          }
          this.page.current++
          this.visibleImgPage++
          this.getTimeImageData()
        } else {
          // 当前图片列表显示的分页visibleImgPage 不是接口请求数据的最新分页current
          this.visibleImgPage++
          this.changeVisibleImg()
        }
      } else {
        // 不是当前列表最后一张图片 直接切换下一张
        this.urlIndex++
        this.updateImageUrl(this.visibleImglist[this.urlIndex].imageUrl, this.urlIndex)
      }
    },
    // 更新图片时预加载
    updateImageUrl(newUrl, index) {
      const img = new Image()
      img.src = newUrl

      // 监听图片加载完成
      img.onload = () => {
        this.url = newUrl
        this.swiper.slideTo(index, 200, true)
      }
    },
    close() {
      this.clearIntervalMethods()
      document.removeEventListener('keydown', this.eventListener)
      this.urlIndex = 0
      this.value1 = []
      this.url2 = []
      this.visibleImglist = []
      this.visibleImgPage = 1
      this.page.current = 1
      this.page.pages = 0
      this.itemPlaceId = ''
      this.isShow = false
      this.placeId = '',
        this.$emit('update:isShow', this.isShow)
      this.$emit('close')
    },
    clearIntervalMethods() {
      if (this.setIntervalObj) {
        clearInterval(this.setIntervalObj)
        this.setIntervalObj = null
      }
    },
    urlClick(item, index) {
      this.urlIndex = index
      this.url = this.visibleImglist[this.urlIndex].imageUrl
      this.swiper.slideTo(this.urlIndex, 200, true)
    },
    getTimeImageData() {
      if (!this.startDate) {
        this.startDate = new Date()
        this.endDate = new Date()
        this.startDate.setHours(0);
        this.startDate.setMinutes(0);
        this.startDate.setSeconds(0);
        this.value1[0] = dateFormat(this.startDate)
        this.value1[1] = dateFormat(this.endDate)
        this.$set(this.value1, 0, dateFormat(this.startDate))
        this.$set(this.value1, 1, dateFormat(this.endDate))
        this.startDate = dateFormat(this.startDate)
        this.endDate = dateFormat(this.endDate)
      } else {
        this.value1[0] = this.startDate
        this.value1[1] = this.endDate
        this.$set(this.value1, 0, this.startDate)
        this.$set(this.value1, 1, this.endDate)
      }
      this.imgLoading = true
      this.loading = true
      console.log("placeId", this.placeId)
      getTimeImage(this.page.current, this.page.size, this.placeId, this.startDate, this.endDate, this.timeSort).then(res => {
        let list = res.data.data.records
        this.url2 = list
        this.page.pages = res.data.data.pages
        this.total = res.data.data.total
        this.changeVisibleImg()
      }).finally(() => {
        this.imgLoading = false
        this.loading = false
      })
    },
    /**
     * 截取显示的图片列表
     * 0-29  30 - 59
     */
    changeVisibleImg() {
      // let startIndex = (this.visibleImgPage - 1) * this.page.size
      // let endIndex = Math.min(this.visibleImgPage * this.page.size, this.url2.length)
      this.visibleImglist = this.url2
      this.urlIndex = 0
      this.url = this.visibleImglist[this.urlIndex].imageUrl
      this.swiper.slideTo(this.urlIndex, 200, true)
      console.log(this.visibleImglist)
      //强制刷新页面
      this.$forceUpdate();
    },
  }
}
</script>
<style scoped lang="scss">
.box_title {
  cursor: pointer;
}

.box_title:hover {
  color: #409eff;
}

.swiper-container {
  width: 100%;
}

/deep/.swiper-wrapper {
  align-items: center !important;
}

.swiper-slide {
  border: 1px solid #dfdfdf;
  border-radius: 5px;
  background: #fff;
  color: #fff;
  height: 100px;
  width: auto;
  /*根据内容调整宽度*/
}

.swiper-slide-active {
  border: 2px #3388fb solid;
  border-radius: 5px;
  background: #fff;
  color: #fff;
  height: 110px;
  width: auto;
  /*根据内容调整宽度*/
}

.swiper-button-prev {
  display: none;
}

.image-container {
  transition: background-image 0.5s ease;
  /* 调整持续时间和动画曲线 */
}
</style>

<style scoped lang="scss">
.my-radio-group {
  background: #FFF;
  padding: 0 10px;
  margin: 0;
  font-size: 12px;
  line-height: 30px;
  text-align: center;
  color: #333333;
  border: 0.5px solid #ccc;
}

.my-radio-group-selected {
  background: #409EFF;
  color: #ffffff;

}

.left_radio_group {
  border-radius: 5px 0 0 5px;
  border-right: none;
}

.right_radio_group {
  border-radius: 0 5px 5px 0;
  border-left: none;
}


// 修改后的动画样式
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

// 显示时从左往右
.slide-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

// 隐藏时从右往左
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 初始状态和结束状态
.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}

.frame-image-box {
  position: absolute;
  z-index: 99999;
  top: 0;
  bottom: 10px;
  left: 10px;
  right: 10px;
  //background-color: rgba(0,0,0,0.3);
  background: #f0f2f5;

  .left-box {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: absolute;
    left: 0;
  }

  .left-box:hover {
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
  }

  .page-turning {
    width: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #c5c0c0;
  }

  .page-turning:hover {
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
  }

  .right-box {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: absolute;
    right: 0;
  }

  .right-box:hover {
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.frame-image-card {
  height: 100%;

  /deep/.el-card__body {
    height: calc(100% - 53px) !important;
    padding: 0px !important;
  }

  /deep/.el-card__header {
    margin-bottom: 0px !important;
  }
}

.image-Box {
  /* 隐藏默认的滚动条样式 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.image-Box::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, and Opera */
}

.imageBox {
  width: 100px;
  height: 90px;
  padding: 5px;
}

.imageEL-box {
  width: 100px;
  height: 100px;
  border: 2px solid #3388fb;
  animation: slide 0.1s ease-in-out;
  /* 设置动画 */
}

.slide-left-animation {
  animation-name: slide-left;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

@keyframes slide-left {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-10%);
  }
}
</style>
