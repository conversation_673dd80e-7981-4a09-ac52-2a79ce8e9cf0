<template>
  <div class="wrapper">
    <div class="header">
      车位事件
    </div>
    <div class="content" @scroll="handleScroll" ref="contentRef">
      <el-timeline>
        <el-timeline-item v-for="item in timelineList" :key="item.id" :timestamp="item.eventTime" placement="top"
          icon="el-icon-time" size="large" color="#409eff">
          <div class="item_box">
            <div class="item_title">
              <div class="item_title_text">{{ item.title }}</div>
              <div class="item_view" v-show="item.parkingOrderId && item.parkingOrderId != -1">
                <el-tooltip class="item" effect="dark" content="停车记录" placement="top-start">
                  <el-button type="text" icon="el-icon-document" size="medium"
                    @click="parkingRecord(item.parkingOrderId)"></el-button>
                </el-tooltip>
              </div>
            </div>
            <div class="item_content">
              {{ item.content }}
            </div>
            <div class="item_user" v-if="item.operatorId && item.operatorId != -1">
              操作人：{{ item.operatorName }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>

      <!-- 加载更多提示 -->
      <div class="load-more" v-if="showLoadMore">
        <span v-if="loading">
          <i class="el-icon-loading"></i> 加载中...
        </span>
        <span v-else-if="noMore">没有更多数据</span>
        <span v-else>上拉加载更多</span>
      </div>
    </div>
    <detail ref="detail"></detail>
  </div>
</template>

<script>
import { getPlaceEventLog } from '@/api/public'
import detail from '@/views/parkingSpace/components/detail.vue';
export default {
  components: {
    detail
  },
  name: 'IfrmeTimeLien',
  props: {
    value: {
      type: Boolean,
      default: true
    },
    itemPlaceId: {
      type: String,
      default: ''
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    }
  },
  watch: {
    itemPlaceId(newVal) {
      if (newVal) {
        this.reset()
      }
    },
    startDate(newVal) {
      if (newVal) {

        this.reset()
      }
    },
    endDate(newVal) {
      if (newVal) {
        this.reset()
      }
    },
  },
  data() {
    return {
      scrollTimer: null,
      loading: false,
      noMore: false,
      showLoadMore: true,
      page: 1,
      pageSize: 10,
      timelineList: [],
      sortStr: 'ascs',
    }
  },
  methods: {
    changeSort(sotrStr) {
      this.page = 1
      this.noMore = false
      this.sortStr = sotrStr;
      this.loadData()
    },
    parkingRecord(orderId) {
      // this.$refs.detail.getDetail(orderId);
      // this.$refs.detail.show();
      this.$emit('parkingRecord', orderId)
    },
    handleScroll(e) {
      if (this.scrollTimer) return
      if (this.loading || this.noMore) return

      this.scrollTimer = setTimeout(() => {
        const { scrollTop, scrollHeight, clientHeight } = e.target
        const bottomDistance = scrollHeight - scrollTop - clientHeight

        if (bottomDistance < 50) {
          this.loadData()
        }
        this.scrollTimer = null
      }, 100)
    },

    async loadData() {
      if (this.loading || this.noMore) return
      if (!this.startDate || !this.endDate) return
      if(!this.itemPlaceId) return
      this.loading = true
      try {
        // 这里替换成你的实际 API 调用
        const res = await getPlaceEventLog(this.itemPlaceId, this.page, this.pageSize, this.startDate, this.endDate, this.sortStr)
        console.log('res', res)
        if (res.data.data.records && res.data.data.records.length > 0) {
          // 追加新数据
          this.timelineList = [...this.timelineList, ...res.data.data.records]
          if (this.timelineList.length >= res.data.data.total) {
            this.noMore = true
          }
          this.page += 1
        } else {
          this.noMore = true
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 重置列表
    reset() {
      this.page = 1
      this.noMore = false
      this.timelineList = []
      this.loadData()
    }
  },
  //   beforeDestroy() {
  //     if (this.scrollTimer) {
  //       clearTimeout(this.scrollTimer)
  //       this.scrollTimer = null
  //     }
  //   }
}
</script>

<style scoped lang="scss">
// 修改后的动画样式
.slide-enter-active,
.slide-leave-active {
  transition: all 0.1s ease;
}

// 显示时从左往右
.slide-enter-from {
  transform: translateX(-100%);
  opacity: 0;
}

// 隐藏时从右往左
.slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

// 初始状态和结束状态
.slide-enter-to,
.slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}

// 原有样式
.wrapper {
  width: 350px;
  height: calc(100vh - 420px);
  overflow-y: auto;
  border-left: 1px solid #f5f5f5;

  .header {
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #f8f8f8;
    font-size: 14px;
  }

  .content {
    height: calc(100% - 60px);
    padding: 10px;
    overflow-y: auto;

    /deep/.el-timeline {
      padding-left: 0;
    }

    /deep/.el-timeline-item__content {
      border: 1px solid #eee;
      border-radius: 5px;
    }

    /deep/.el-timeline-item__timestamp {
      font-size: 14px;
      color: #409eff;
    }

    .item_box {
      .item_title {
        padding: 10px;
        font-size: 16px;
        color: #333;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px dashed #eee;

        // .item_view{
        //   cursor: pointer;
        // }
        /deep/.el-button {
          padding: 0;
        }
      }

      .item_content {
        font-size: 14px;
        color: #5e6d82;
        padding: 10px;
      }

      .item_user {
        padding: 10px;
        border-top: 1px dashed #eee;
        font-size: 13px;
        color: #909399;
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: 10px 0;
  color: #909399;
  font-size: 14px;

  .el-icon-loading {
    margin-right: 5px;
  }
}

// 可以添加一些过渡动画
.timeline-item-enter-active,
.timeline-item-leave-active {
  transition: all 0.3s;
}

.timeline-item-enter,
.timeline-item-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
</style>
