<template>
	<el-select
		class="el-select-dense"
		v-model="parkLotName"
		placeholder="选择车场"
		:clearable="true"
		refs="mySelect"
		:reserve-keyword="true"
		filterable
		:multiple="multiple"
		popper-class="sele"
		:filter-method="filter"
		@change="change"
		@clear="clearSelect"
		@blur="funb"
		@focus="focus"
		@visible-change="hidden">
		<el-option v-for="item in optionfen" :key="item.name" :label="item.name" :value="item.id" placeholder="请输入车场名称"></el-option>
		<div style="bottom: -10px; margin-top: 10px; text-align: right">
			<el-pagination
				background
				@current-change="handleCurrentChange"
				:current-page="currentpage"
				:page-size="pageSize"
				layout="prev, pager,next,total"
				:total="totalSize"></el-pagination>
		</div>
	</el-select>
</template>
<script>
import { queryParklots } from '@/api/park/parklot';
/*
 *slot:Avue插槽名称;
 *multiple:多选开关;
 */
export default {
	data() {
		return {
			options: [], //总数据
			optionfen: [], //当前页码的数据
			parkLotName: '', //输入框的值
			totalSize: 0,
			currentpage: 1, //当前页码
			pageSize: 10, //每页展示的条数
		};
	},
	props: {
		value: {
			type: String,
		},
		//向父组件传递数据
		multiple: {
			type: Boolean,
			default: false,
		},
	},
	computed: {},
	created: function () {
		// 初始化查询
		this.queryParklots(this.parkLotName, this.pageSize, this.currentpage);
	},
	mounted() {},
	methods: {
		// 查询数据
		queryParklots(name, pageSize, currentpage) {
			var params = {
				name: name,
			};
			var _this = this;
			queryParklots(currentpage, pageSize, Object.assign(params)).then(res => {
				_this.optionfen = [];
				res.data.data.records.forEach((elem, index) => {
					_this.optionfen.push({ name: elem.name, id: elem.id });
				});
				if (1 === currentpage) {
					_this.totalSize = res.data.data.total;
				}
			});
		},
		//分页的实现,currentpage 为页码，每页展示的数据为10（可自行更改pageSize）条，分别是在总数据options中
		//下标从(currentpage -1)*10开始的十条数据
		handleCurrentChange(currentpage) {
			this.queryParklots(this.parkLotName, this.pageSize, currentpage);
		},
		//选择数据后将相关数据发送给父组件
		change() {
			let arr = [];
			this.val = this.parkLotName;
			for (let j in this.optionfen) {
				if (this.parkLotName == this.optionfen[j].id) {
					arr.push(this.optionfen[j]);
				}
			}
			this.returnValue(arr);
		},
		// return
		returnValue(arr) {
			if (!this.multiple) {
				this.$emit('input', arr[0]);
				return;
			}
			this.$emit('input', arr);
		},
		// 获得焦点
		//获得焦点的时候跳转到当前value所在的页码
		focus() {
			if (this.optionfen.length != 0) {
				return;
			}
			this.handleCurrentChange(this.currentpage);
		},
		// 失去焦点
		//前面每次操作都将输入框内的value值存储一份到val中，就是为了防止用户搜索的时候中途关闭选择框，这个时候输入框显示的就是
		//用户输入一半的value值，加上这层逻辑就可以在用户输入的数据在总数据中不存在的时候(也就是无效数据)，关闭选择框
		//之后让输入框依旧显示上一次的正确value值
		funb() {
			// $('.drop >>> .el-input__inner').css({
			// 	color: 'white',
			// });
      this.$emit('input', this.parkLotName);
		},
		hidden(bool) {
			// 隐藏select列表
			if (!bool) {
				// 移除mousedown事件监听
				removeEventListener('mousedown', function () {}, false);
			} else {
				this.focus();
				// 打开select列表
				// 增加mousedown事件监听  当点击分页时移除输入框的默认事件 ，让他不会失去焦点（blur),如果不加，就会
				//出现当用户点击分页之后，输入框会失去焦点，这个时候如果用户需要输入数据进行搜索就需要先删除输入框的值再输入，体验不好。
				//（elementUI下拉框的默认样式，当可搜索时点击输入框可直接输入，不需要删除上次数据）
				document.addEventListener(
					'mousedown',
					function (e) {
						// console.log(e)
						if (e.target.tagName === 'LI' || (e.target.tagName == 'I' && e.target.localName == 'i')) {
							e.preventDefault();
						}
					},
					false,
				);
			}
		},
		//搜索方法,将符合的数据存入options中，并分页展示
		filter(val) {
			this.optionfen = [];
			this.parkLotName = val;
			let arr = [];
			let value = val.toLowerCase();
			this.options = arr;
			this.handleCurrentChange(1);
		},
		// 选择数据清空
		clearSelect() {
			this.parkLotName = '';
			this.currentpage = 1;
			this.totalSize = 0;
			this.optionfen = [];
			this.returnValue([{}]);
		},
	},
	//监听来自父组件的数据，当数据更新时，重置展示
	watch: {
		renderingAllKeys: {
			//深度监听，可监听到对象、数组的变化
			handler(newV) {
				if (newV.length > 0) {
					this.options1 = newV;
					this.options = newV;
					this.optionfen = [];
					this.currentpage = 1; //标记重置
					let start = (this.currentpage - 1) * this.pageSize;
					let end = Number(start) + Number(this.pageSize);
					if (end > this.options1.length) {
						end = this.options1.length;
					}
					for (let i = start; i < end; i++) {
						this.optionfen.push(this.options1[i]);
					}
					this.parkLotName = newV[0].label; //指标重置
					this.val = newV[0].label; //指标重置
					this.fun();
				} else {
					this.options1 = [];
					this.options = [];
					this.optionfen = [];
					this.parkLotName = '';
				}
			},
			deep: false,
		},
	},
};
</script>

<style lang="scss" scoped>
.drop >>> .el-input__inner {
	background: #5183ff !important;
	color: white;
	border: none;
	height: 26px;
	padding: 10px 22px 10px 10px;
	text-align: center;
}
.drop {
	width: 250px;
}
.drop >>> .el-select .el-input .el-select__caret {
	display: none;
}
.el-select-dense {
	--height: 33px;
	//el-select本身的高度
	/deep/ input {
		height: var(--height);
	}
	//el-select右边的图标
	/deep/ .el-input__icon {
		line-height: var(--height);
	}
}
</style>
