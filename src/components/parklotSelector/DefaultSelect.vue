<template>
  <div class="selectBox">
    <div v-if="ParklotType==0">
      <el-select
        v-model="ids" :filterable="IssFilterable" :multiple="IsMultiple" remote :remote-method="remoteMethod"
        collapse-tags :clearable="IsClearable" :placeholder="placeholder" @change="change" @clear="clear">
        <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.id"
        ></el-option>
        <div style="bottom: -10px; margin-top: 10px; text-align: right">
          <el-pagination
            v-if="IsPagination"
            background
            @current-change="handleCurrentChange"
            :current-page="currentpage"
            :page-size="pageSize"
            layout="prev, pager,next,total"
            :total="totalSize"></el-pagination>
        </div>
      </el-select>
    </div>
    <div v-else>


      <el-select v-model="ids" :filterable="IssFilterable" :multiple="IsMultiple"
                 collapse-tags :clearable="IsClearable" :placeholder="placeholder" @change="change" @clear="clear">
        <el-option v-for="item in options" :key="item.name" :label="item.name" :value="item.id"
        ></el-option>
      </el-select>

    </div>
  </div>
</template>
<script>
import {queryParklots, userParklotOutList, parklotInList} from '@/api/park/parklot';
export default {


  props: {
    value: [String, Object, Array],
    // 是否禁用
    IsDisabled: {
      type: Boolean,
      default: false
    },
    // 是否清空
    IsClearable: {
      type: Boolean,
      default: false
    },
    // 是否多选
    IsMultiple: {
      type: Boolean,
      default: false
    },
    // 是否默认
    IsDefault: {
      type: Boolean,
      default: false
    },
    // 是否搜索
    IssFilterable: {
      type: Boolean,
      default: false
    },
    // 车场类型 0全部  1路外 2路内
    ParklotType: {
      default: 0
    },
    // 是否分页
    IsPagination: {
      type: Boolean,
      default: true
    },
    // 占位符
    placeholder: {
      type: String,
      default: "选择车场"
    }

  },

  data() {
    return {
      parklot: {},
      options: [], //总数据
      optionfen: [], //当前页码的数据
      parkLotName: '', //输入框的值
      totalSize: 0,
      currentpage: 1, //当前页码
      pageSize: 10, //每页展示的条数
      ids: undefined,
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {

          if (this.IsMultiple) {

            this.ids = val.split(',')
          } else {
            this.ids = val
          }
        } else {
          this.ids=undefined
        }
      },
      deep: true,
      immediate: true
    },
  },

  created() {
    if (this.ParklotType == 0 & this.IsPagination) {
      this.pageSize = 10
    } else {
      this.pageSize = 9999
    }
    this.getList(undefined)
  },
  methods: {
    getList(name) {
      if (this.ParklotType == 0) {
        var params = {
          name: name,
        };
        queryParklots(this.currentpage, this.pageSize, Object.assign(params)).then(res => {
          this.options = [];
          res.data.data.records.forEach((elem) => {
            this.options.push({name: elem.name, id: elem.id});
          });

          if (this.IsDefault) {
            if (this.IsMultiple) {
              // if (this.currentpage == 1) {
              //   this.value = [this.options[0].id]
              // }
              if (this.ids.length == 0) {
                this.ids = [this.options[0].id]
                this.setData(this.options[0].id)
              }

            } else {


              if (!this.ids) {
                this.ids = this.options[0].id
                this.setData(this.options[0].id)
              }

            }
          }
          this.totalSize = res.data.data.total;
        })
      }
      if (this.ParklotType == 1) {
        userParklotOutList().then(res => {
          this.options = [];
          res.data.data.forEach((elem) => {
            this.options.push({name: elem.name, id: elem.id});
          });
          if (this.IsDefault) {
            if (this.IsMultiple) {

              if (this.ids.length == 0) {
                this.ids = [this.options[0].id]
                this.setData(this.options[0].id)
              }

            } else {

              if (!this.ids) {
                this.ids = this.options[0].id
                this.setData(this.options[0].id)
              }
            }
          }
        })
      }
      if (this.ParklotType == 2) {
        parklotInList().then(res => {
          console.log(res, 'lunei')
          this.options = [];
          res.data.data.forEach((elem) => {
            this.options.push({name: elem.name, id: elem.id});
          });
          if (this.IsDefault) {
            if (this.IsMultiple) {
              if (this.ids.length == 0) {
                this.ids = [this.options[0].id]
                this.setData(this.options[0].id)
              }
            } else {
              if (!this.ids) {
                this.ids = this.options[0].id
                this.setData(this.options[0].id)
              }

            }
          }
        })
      }
    },
    handleCurrentChange(currentpage) {
      this.currentpage = currentpage
      this.getList(undefined)
    },
    change(e) {
      if (this.IsMultiple) {
        if (e.length > 0) {
          // console.log('多选有数据')
          let ids = e.join(",")
          this.setData(ids)
        } else {
          if (this.IsDefault) {
            this.$message.warning('请选择至少一个停车场');
            this.ids = [this.options[0].id]
            this.setData(this.options[0].id)
          }
        }
      } else {
        this.setData(e)
      }
    },
    clear() {
      if (this.IsDefault) {
        this.$message.warning('请选择至少一个停车场');
        if (this.IsMultiple) {
          this.ids = [this.options[0].id]
          this.setData(this.options[0].id)
        } else {
          this.ids = this.options[0].id
          this.setData(this.options[0].id)
        }
      } else {
        this.setData(undefined)
      }
    },
    setData(ids) {
      this.$emit("input", ids);
      this.$emit("change");
    },
    remoteMethod(query) {
      this.getList(query)
    }
  },
};
</script>

<style lang="scss" scoped>
.drop > > > .el-input__inner {
  background: #5183ff !important;
  color: white;
  border: none;
  height: 26px;
  padding: 10px 22px 10px 10px;
  text-align: center;
}

.el-scrollbar__view {
  background: yellow !important;
}

.drop > > > .el-select .el-input .el-select__caret {
  display: none;
}

.selectBox {
  .el-scrollbar__view {
    background: yellow;
  }
}
</style>
