<template>
  <div>
    <el-select
      style="width: 175px"
      v-model="firstLevelvalue"
      placeholder="请选择小区"
      size="small"
      @change="changeFirstCode"
    >
      <el-option
        v-for="item in firstLevelName"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
      </el-option>
    </el-select>
    <span>&ensp;</span>
    <el-select
      style="width: 175px"
      v-model="secondLevelValue"
      placeholder="请选择组团"
      size="small"
      @change="changeSecondCode"
    >
      <el-option
        v-for="item in secondLevelName"
        :key="item.id"
        :label="item.name"
        :value="item.id"
        :size="small"
      >
      </el-option>
    </el-select>
    <span>&ensp;</span>
    <el-select
      style="width: 175px"
      v-model="thirdLevelValue"
      placeholder="请选择楼栋"
      size="small"
      @change="changeThirdCode"
    >
      <el-option
        v-for="item in thirdLevelName"
        :key="item.id"
        :label="item.name"
        :value="item.id"
        :size="small"
      >
      </el-option>
    </el-select>
    <span>&ensp;</span>
    <el-select
      style="width: 175px"
      v-model="fourLevelValue"
      placeholder="请选择单元"
      size="small"
      @change="changeFourCode"
    >
      <el-option
        v-for="item in fourLevelName"
        :key="item.id"
        :label="item.name"
        :value="item.id"
        :size="small"
      >
      </el-option>
    </el-select>
    <span>&ensp;</span>
    <el-select
      style="width: 175px"
      v-model="fiveLevelValue"
      placeholder="请选择房号"
      size="small"
      @change="changeFiveCode"
    >
      <el-option
        v-for="item in fiveLevelName"
        :key="item.id"
        :label="item.name"
        :value="item.id"
        :size="small"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
  import {
    getCommunityAddressdetail,
    getFloorAddressDetail,
    getUnitAddressDetail,
    getBuildingAddressDetail,
    getGroupAddressDetail,
    getCommunityAddress,
    getGroupAddress,
    getBuildingAddress,
    getUnitAddress,
    getFloorAddress
  } from "@/api/common/address";

  export default {
    name: "communityAddress",
    props: {
      form: {
        type: Object,
        default: () => {
          return {};
        }
      },
      currentDetailRow: {
        //回显数据
        type: Object,
        default: () => {
          return {};
        }
      },
      currentFormStatus: {
        //是否需要回显 1.是
        type: Number,
        default: () => {
          return 1;
        }
      },
    },
    data() {
      return {
        DoorDeviceCommunity: {},
        firstLevelName: [],
        firstLevelvalue: "",
        secondLevelName: [],
        secondLevelValue: "",
        thirdLevelName: [],
        thirdLevelValue: "",
        fourLevelName: [],
        fourLevelValue: "",
        fiveLevelName: [],
        fiveLevelValue: "",
        addressValue: [],
        addressName: [],
        userInfo: "",
        communityName: "",
        groupName: "",
        buildingName: "",
        unitName: "",
        floorName: "",
        community: {},
        group: {},
        building: {},
        unit: {},
        floor: {},
      };
    },
    created() {
      this.userInfo = this.currentDetailRow.userInfo;
      this.getCommunityList();
    },
    watch: {
      firstLevelvalue(newValue) {
        if (newValue) {
          this.changeSecondCode(newValue);
          this.form.communityId = newValue;
        }
      },
      secondLevelValue(newValue) {
        if (newValue) {
          this.changeThirdCode(newValue);
          this.form.groupId = newValue;
        }
      },
      thirdLevelValue(newValue) {
        if (newValue) {
          this.changeFourCode(newValue);
          this.form.buildingId = newValue;
        }
      },
      fourLevelValue(newValue) {
        if (newValue) {
          this.changeFiveCode(newValue);
        }
      }
    },
    methods: {
      getCommunityList() {
        getCommunityAddress(this.userInfo).then((res) => {
          this.firstLevelName = res.data.data;
          this.firstLevelvalue = this.form.communityId;
        });
      },
      changeFirstCode: function (val) {
        getGroupAddress(val).then(res => {
          this.secondLevelName = res.data.data;
          this.secondLevelValue = this.form.groupId;
        });
      },
      changeSecondCode(val) {
        getGroupAddress(val).then(res => {
          this.secondLevelName = res.data.data;
          this.secondLevelValue = this.form.groupId;
        });
      },
      changeThirdCode(val) {
        getBuildingAddress(val).then(res => {
          this.thirdLevelName = res.data.data;
          this.thirdLevelValue = this.form.buildingId;
        });
      },
      changeFourCode(val) {
        getUnitAddress(val).then(res => {
          this.fourLevelName = res.data.data;
          this.fourLevelValue = this.form.unitId;
        });
      },
      changeFiveCode(val) {
        getFloorAddress(val).then(res => {
          this.fiveLevelName = res.data.data;
          this.fiveLevelValue = this.form.floorId;
        });
      },
      getAllAddress() {
        return this.DoorDeviceCommunity;
      },
      async setAllAddress(provinceCode, cityCode, areaCode) {
        let result1 = await getProvinceAddress("86");
        this.firstLevelName = result1.data.data;
        this.firstLevelvalue = provinceCode;
        let result2 = await getProvinceAddress(this.firstLevelvalue);
        this.secondLevelName = result2.data.data;
        this.secondLevelValue = cityCode;
        let result3 = await getProvinceAddress(this.secondLevelValue);
        this.thirdLevelName = result3.data.data;
        this.thirdLevelValue = areaCode;
        //给标签赋值
        this.addressValue = [];
        this.addressValue.push(
          this.firstLevelvalue,
          this.secondLevelValue,
          this.thirdLevelValue
        );
      },

      setAllAddress(provinceCode, cityCode, areaCode) {
        this.firstLevelvalue = provinceCode;
        console.log(this.firstLevelvalue);
        this.changeFirstCode();
        setTimeout(() => {
          this.secondLevelValue = cityCode;
          this.changeSecondCode();
          setTimeout(() => {
            this.thirdLevelValue = areaCode;
            this.changeThirdCode();
          }, 100);
        }, 100);
      },
    }
  };
</script>
