<template>
  <div v-if="isShow" class="app-license-keyboard" :class="[customClass]">
    <!---->
    <div class="license-container" :class="[customLicenseClass]" :style="{background:keyboardBg,opacity,fontSize}">
      <div class="license-bar">
        <div style="width: 100%;height: 30px;line-height: 30px;display:flex;justify-content:space-between;">
          <span class="close" style="font-size: 18px" @click="close()">关闭</span>
          <span class="close"><input class="view" readonly v-model="licenseNumber" /></span>
          <span class="close" style="font-size: 18px;color:green;float:right" @click="confirm()">确定</span>
        </div>
      </div>
      <template v-if="type">
        <template v-for="(items,k) in licenseNumberModels">
          <div :key="k" class="license-buttons">
            <span v-if="k== 3" class="model" :style="keyStyle" @click="toggle()">
              切换
            </span>
            <template v-for="(item,j) in items">
              <span :key="j" @click="handleChinese(item)" :style="keyStyle">{{ item }}</span>
            </template>
            <span v-if="k== 3" class="delete" :disabled="!licenseNumber" :class="{'disabled-btn':!licenseNumber}" :style="keyStyle" @click="deleteLicense()">删除</span>
          </div>
        </template>
      </template>
      <template v-else>
        <template v-for="(items,k) in abcAndNums">
          <div :key="k" class="license-buttons">
            <span v-if="k== 3" class="model" :style="keyStyle" @click="toggle()">
              切换
            </span>
            <template v-for="(item,j) in items">
              <span :key="j" @click="handleAbcNum(item)" :style="keyStyle">{{ item }}</span>
            </template>
            <span v-if="k== 3" class="delete" :disabled="!licenseNumber" :class="{'disabled-btn':!licenseNumber}" :style="keyStyle" @click="deleteLicense()">删除</span>
          </div>
        </template>
      </template>
    </div>
    <div class="model-box" @click="close"></div>
  </div>
</template>
<script>
import { isVehicleNumber } from '@/util/rules'
const theme = '#3d51aa';
export default {
  name: 'LicenseKeyboard',
  model: {
    value: 'license',
    event: 'input'
  },
  props: {
    license: {
      required: false,
      type: [String],
      default: null
    },

    //顶级节点自定义的class
    customClass: {
      required: false,
      type: [String],
      default: null
    },

    //键盘容器节点自定义的class
    customLicenseClass: {
      required: false,
      type: [String],
      default: null
    },

    //键盘背景
    keyboardBg: {
      required: false,
      type: [String],
      default: null
    },

    //键盘字体颜色
    fontColor: {
      required: false,
      type: [String],
      default: null
    },

    //键盘按钮的边框颜色
    keyBorderColor: {
      required: false,
      type: [String],
      default: theme
    },

    //键盘背景
    keyBg: {
      required: false,
      type: [String],
      default: null
    },

    //键盘的透明度
    opacity: {
      required: false,
      type: [Number],
      default: 1
    },

    //默认的字体大小 16px => 1rem
    fontSize: {
      required: false,
      type: [String],
      default: '1rem'
    },

    //键盘的默认圆角
    borderRadius: {
      required: false,
      type: [String],
      default: null
    },

    //是否只在移动端使用 默认false,在pc端使用的时候,建议全部使用手动配置字体大小的方法,区别在于是否会开启字体响应,其中使用了autosize的方法,会改变跟元素的font-size
    isMobile: {
      required: false,
      type: [Boolean],
      default: false
    }
  },
  computed: {
    keyStyle() {
      return {
        color: this.fontColor,
        background: this.keyBg,
        borderColor: this.keyBorderColor,
        fontSize: this.fontSize,
        borderRadius: this.borderRadius
      }
    }
  },
  data() {
    return {
      isShow: false,
      licenseNumberModels: [
        ['川', '京', '苏', '津', '浙', '渝', '冀', '皖', '琼', '鲁'],
        ['闽', '贵', '新', '晋', '赣', '云', '宁', '蒙', '豫', '藏'],
        ['港', '辽', '桂', '陕', '澳', '吉', '湘', '甘', '台', '粤'],
        ['青', '沪', '鄂', '黑', '军', '警', '学', '使', '领']
      ],
      abcAndNums: [
        [1, 2, 3, 4, 5, 6, 7, 8, 9, 0],
        [`Q`, `W`, `E`, `R`, `T`, `Y`, `U`, `I`, `O`, `P`],
        [`A`, `S`, `D`, `F`, `G`, `H`, `J`, `K`, `L`],
        [`Z`, `X`, `C`, `V`, `B`, `N`, `M`]
      ],
      type: true,
      licenseNumber: '贵', //车牌输入的值,
      isLegal: false,
      // 兼容处理
      oldLicenseNumber:''
    };
  },
  watch: {
    licenseNumber(val) {
      this.isLegal = false;
      if (val && val.length >= 7) {
        // 屏蔽车牌校验
        // this.isLegal = isVehicleNumber(val) ? true : false;
        this.isLegal = true;
      }
      //删除完的时候返回到初始界面
      if (!val) {
        this.type = true;
      }
    }
  },
  mounted() {
    if (this.isMobile) {
      this.init();
    }

  },
  methods: {
    init() {
      !(function (win, doc) {
        const setFontSize = () => {
          // 获取window 宽度
          // zepto实现 $(window).width()就是这么干的
          let winWidth = window.innerWidth;
          let size = (winWidth / 375) * 16;
          doc.documentElement.style.fontSize = size + 'px';
        }
        let evt = 'onorientationchange' in win ? 'orientationchange' : 'onresize';
        let timer = null;
        win.addEventListener(evt, function () {
          clearTimeout(timer);
          timer = setTimeout(setFontSize, 300);
        }, false);
        win.addEventListener('pageshow', function (e) {
          if (e.persisted) {
            clearTimeout(timer);
            timer = setTimeout(setFontSize, 300);
          }
        }, false);
        //初始化
        setFontSize();
      }(window, document));
    },

    /**
     * 弹出键盘
     */
    show(val) {
      this.isShow = true;
      if (val) {
        this.licenseNumber = val;
        this.$emit('input', this.licenseNumber);
        this.type = false;
        return;
      }
      this.type = true;
    },

    /**
     * 切换
     */
    toggle() {
      // if (this.licenseNumber) {
      this.type = !this.type;
      // }
    },

    /**
     * 汉字输入
     */
    handleChinese(key) {
      if (!this.licenseNumber || this.licenseNumber.length == 1) {
        this.licenseNumber = key;
        //输入1个汉字切换到英文输入
        this.type = false;
        this.$emit('input', this.licenseNumber);
      } else if (this.licenseNumber.length < 8) {
        this.licenseNumber += key;
        this.$emit('input', this.licenseNumber);
      }
    },

    /**
     * 数字和字母输入
     */
    handleAbcNum(key) {
      if (this.licenseNumber.length < 8) { //最多允许输入8位
        this.licenseNumber += key;
        this.$emit('input', this.licenseNumber);
      }
    },

    /**
     * 删除车牌
     */
    deleteLicense() {
      if (this.licenseNumber) {
        this.licenseNumber = this.licenseNumber.substring(0, this.licenseNumber.length - 1);
        this.$emit('input', this.licenseNumber);
      }
    },

    /**
     * 完成
     */
    confirm() {
      if (!this.isLegal) {
        this.$message.error("请输入正确的车牌号")
        return;
      }
      this.$emit('update:license', this.licenseNumber)
      if(this.oldLicenseNumber==this.licenseNumber){
        // 当inputKeyboard 输入上一个删除的车牌 ,导致inputKeyboard中的watch license_ 无响应,故在此做兼容处理
        this.$emit('equalLicenseDealFun_', this.licenseNumber)
      }
      this.oldLicenseNumber = JSON.parse(JSON.stringify(this.licenseNumber))
      this.$nextTick(() => {
        this.licenseNumber = null
        this.isShow = false;
      })

    },

    /**
     * 关闭
     */
    close() {
      // this.licenseNumber = "贵"
      this.$nextTick(() => {
        this.licenseNumber = null
        this.isShow = false;
      })
    },
  }
};
</script>
<style lang="scss" scoped>
.app-license-keyboard {
  position: fixed;
  z-index: 9999;
  bottom: 0;
  left: 0;
  right: 0;
  .icon {
    font-size: 14px;
    cursor: pointer;
  }
  font {
    cursor: pointer;
  }
  .license-container {
    box-sizing: border-box;
    position: relative;
    width: 1000px;
    height: 264px;
    left: 50%;
    margin-left: -500px;
    z-index: 99999999;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px 1px #959696;
    padding-bottom: 10px;
    .license-bar {
      display: flex;
      justify-content: space-between;
      padding: 4px 12px;
      color: #333;
      font-weight: bold;
      font-size: 14px;
      span {
        align-self: center;
      }
      .confirm,
      .close {
        cursor: pointer;
      }
      .close {
        margin-left: 4px;
      }
      .close {
        .view {
          border: 1px solid #e8e8e8;
          padding: 5px 11px;
          box-sizing: border-box;
          border-radius: 5px;
        }
      }
    }
    .license-buttons {
      padding: 0 6px;
      margin: 0 auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      background-color: #eee;
      span {
        flex: 1;
        margin: 2px;
        padding: 4px;
        text-align: center;
        white-space: nowrap;
        color: #333;
        border: 0.0625rem solid #e7e7e7 !important;
        cursor: pointer;
        transition: color, background 0.2s ease-in;
        font-size: 20px !important;
        background: #fff;
        border-radius: 6px;
        &:hover {
          background: #4db8ff;
          color: #fff;
        }
      }
      .model,
      .delete {
        flex: 1;
      }
      .disabled-btn {
        cursor: not-allowed;
      }
    }
  }
}
.model-box {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.1);
}
</style>
