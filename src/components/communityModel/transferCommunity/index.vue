<template>
  <div>
    <el-dialog @close="close" :close-on-click-modal="false" :modal="false" :close-on-press-escape="false" :title="title" :visible.sync="value" width="500px">
      <div class="main">
        <el-transfer v-model="selectCommunityArray" :data="communityArray" :props="props"></el-transfer>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "medium",
  props: {
    value: Boolean,
    communityArray: Array,
    selectCommunityArray: Array,
    title: '选择社区',
  },
  data() {
    return {
      props: {
        key: 'key',
        label: 'value'
      }
    }
  },
  created() {
    console.log(this.selectCommunityArray);
  },
  methods: {
    close() {
      this.$emit('update:value', false);
    },
    save() {
      this.$emit('select', this.selectCommunityArray);
      this.$emit('update:value', false);
    },
  },
  watch: {
  }
}
</script>
<style  lang="scss">
.main /deep/ .el-transfer-panel__list .el-transfer-panel__item {
  width: auto !important;
}
</style>
<style scoped lang="scss">
.main {
  .title {
    font-size: 18px;
    font-weight: 500;
  }
  /deep/ .el-transfer-panel__item {
    width: auto !important;
  }
  /deep/ .el-transfer-panel__item + .el-transfer-panel__item {
    display: inline-block !important;
  }
  .tips {
    margin: 10px 0;
  }

  .payList {
    .payItem {
      border: 1px #e7e7e7 solid;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 80px;
      margin-bottom: 10px;
      cursor: pointer;

      p {
        margin: 0;
      }
    }

    .currentItem {
      background-color: #1f94f4;
      color: #ffffff;
    }
  }
}
</style>
