<template>
  <div>
    <el-input :placeholder="placeholder"
              :clearable="true"
              v-model="text"
              @change="handleChange">
      <template slot="append">
          <menu-icon @click="handleShow" :src="text"></menu-icon>
      </template>
    </el-input>
    <el-dialog :title="placeholder"
               :modal-append-to-body="false"
               append-to-body
               :visible.sync="box"
               width="40%">
      <el-scrollbar style="height:500px;overflow-x:hidden">
        <avue-tabs :option="option"
                   @change="handleTabs"></avue-tabs>

          <div class="grid-container">
            <div class="grid-item" v-for="item in list" @click="handleSubmit(item)" :class="{'active':text===item}">

              <div v-if="item&&item.startsWith('icon')">
                <div class="grid-item-icon"><i :class="item"></i></div>
                <div class="grid-item-name">{{item.split(' ')[1]}}</div>
              </div>

              <div v-else>
                <el-image class="grid-item-img" :src="item">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <div class="grid-item-name">{{item.split('.')[1]}}</div>
              </div>

            </div>
          </div>

      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script>
import menuIcon from "@/components/menuIcon/menuIcon";

export default {
  name: "menu-icon-select",
  components: { menuIcon },
  props: {
    iconList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    src: String,
    placeholder: String,
    blur: Function,
    focus: Function,
    change: Function,
    click: Function,
  },
  data () {
    return {
      text: '',
      box: false,
      tabs: {}
    };
  },
  computed: {
    list () {
      return this.tabs.list || [];
    },
    option () {
      return {
        column: this.iconList
      };
    }
  },
  mounted() {
    this.text = this.src;
  },
  created () {
    this.tabs = this.iconList[0] || {};
  },
  methods: {
    handleTabs (tabs) {
      this.tabs = tabs;
    },
    handleChange() {
      this.$emit('handleChange', this.text);
    },
    handleSubmit (item) {
      this.box = false;
      this.text = item;
      this.$emit('handleChange', this.text);
    },
    handleShow () {
      if (this.disabled || this.readonly) return;
      this.box = true;
    }
  }
};
</script>


<style lang="scss" scoped>
.grid-container {
  display: grid;
  width: 100%;
  background-color: #f0f2f5;
  box-sizing: border-box;
  grid-template-columns: repeat(5, 1fr); /* 创建三列，每列平均分配剩余空间 */
  gap: 1px; /* 设置列之间的间隔 */
  text-align: center;
  padding: 1px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: white;
  color: #99a9bf;
  height: 100px;
}

.grid-item:hover {
  color: #1e80ff;
}

.active {
  color: #1e80ff;
}

.grid-item-img {
  width: 30px;
}

.grid-item-icon i {
  font-size: 28px;
  transition: transform 0.2s ease;
}

.grid-item-icon:hover {
  transform: scale(1.5);
}

.grid-item-img:hover {
  transform: scale(1.5);
}

.grid-item-name {
  font-size: 12px;
  margin-top: 12px;
}

</style>
