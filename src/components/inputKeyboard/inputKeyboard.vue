<template>
	<div class="container">
		<div class="box" :class="[disabled ? 'bgInactive' : 'bgActive']">
			<div class="box_input" @click.stop="onBoxInputClick">
				<!-- <div class="place_str" v-if="licenseList_.length===0">{{palceText}}</div> -->
				<div class="box_license">
					<div class="box_license_item" v-for="(val, index) in licenseList_" :key="index">
						<span @click.stop="onChangeLicense(val)">{{ val }}</span>
						<i class="el-icon-circle-close cus-close" @click.stop="onDeleteLicense(val)"></i>
					</div>
					<div class="box_license_item box_license_item_input" v-if="licenseList_.length == 0 || limit != 1">
						<input
							:disabled="disabled || disableInput"
							ref="customInput"
							:placeholder="limit == 1 ? '输入车牌号(单个车牌)' : '输入车牌号(多车牌以任意字符隔开)'"
							v-model="inputPlateTmp_"
							type="text"
							@keydown.stop="onPlateInputKeyDown"
							@blur.stop="onPlateInputBlur" />
						<div class="keyboard_flag" :class="[keyboardOn ? 'keyboard_on' : 'keyboard_off']" @click.stop="onPlateInputSwitchClick"></div>
					</div>
				</div>
			</div>
		</div>
		<Keyboard ref="keyboard" :license.sync="license_" @equalLicenseDealFun_="dealLicense"></Keyboard>
	</div>
</template>

<script>
/*
用法：
 :limit="1" 限制只能输入一个车牌
 :disabled="Boolean" 禁用所有功能
  两种方式返回值
  1. <InputKeyboard @change="onLicenseChange"></InputKeyboard>
  2. <InputKeyboard :value.sync='arrObj'></InputKeyboard> ((双向绑定) 返回的类型与传入的类型一致)
*/
import keyboard from '../keyboard/keyboard';
import { isVehicleNumber } from '@/util/rules';

export default {
	components: {
		Keyboard: keyboard,
	},
	props: {
		//限制车牌数
		limit: [String, Number],
		// 返回的车牌(字符串数组|字符串逗号分隔)
		value: {
			require: false,
			type: [Array, String],
			default: null,
		},
		disabled: {
			require: false,
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			licenseListMap: new Map(),
			licenseList_: [],
			license_: '',
			licenseObj_: { license_: '' },
			tmp: '',
			// 是否能手动输入
			disableInput: false,
			// 手动输入车牌最终值
			inputPlate_: '',
			// 手动输入车牌临时值
			inputPlateTmp_: '',
			keyboardOn: false,
			whiteList_: ['无牌车'],
			debounce: function () {},
		};
	},
	created() {
		this.setLicenseListMap(this.value, true);
		// 简单的debounce
		this.debounce = (() => {
			let timeount_ = null;
			return func => {
				if (timeount_) {
					clearTimeout(timeount_);
				}
				timeount_ = setTimeout(() => {
					func();
				}, 200);
			};
		})();
	},
	watch: {
		keyboardOn(val) {
			if (val) {
				this.dispatchShowKeyboard('');
			}
		},
		value(val) {
			this.setLicenseListMap(val, false);
		},
		license_(val) {
			let val_ = val.trim();
			if (!val_) {
				return;
			}
			// 避免删除一个车牌再输入相同的车牌不触发监听
			this.licenseObj_ = Object.assign({}, this.licenseObj_, {
				license_: val,
			});
		},
		licenseObj_(val) {
			let val_ = val.license_;
			if (!val_) {
				return;
			}
			let max = this.limit ? parseInt(this.limit) : Infinity;
			// 当tmp存在值时,会先删除之前存在的值
			if (this.tmp || this.inputPlateTmp_) {
				// 删除以前的key
				let arr = Array.from(this.licenseListMap);
				// 可能多个值
				val_ = this.splitPlates(val_);
				//todo 性能问题
				for (let i in arr) {
					if (arr[i][0] == this.tmp) {
						arr[i] = [val_[0], val_[0]];
						val_.splice(0, 1);
						// 在修改一个车牌时又多加了几个车牌
						for (let j in val_) {
							if (arr.length < max) {
								arr.push([val_[j], val_[j]]);
							}
						}
						break;
					}
				}
				this.licenseListMap = new Map(arr);
				this.tmp = '';
				this.inputPlateTmp_ = '';
			} else {
				// 超出输入个数限制
				if (this.checkIsMax(true)) {
					return;
				}
				// val_ = val_.toUpperCase();
				val_ = this.splitPlates(val_);
				for (let i in val_) {
					if (this.licenseListMap.size < max) {
						this.licenseListMap.set(val_[i], val_[i]);
					}
				}
			}
			this.update();
		},
		licenseList_() {
			if (this.checkIsMax()) {
				this.disableInput = true;
				return;
			}
			this.disableInput = false;
		},
		//? 暂时保留
		// 手动输入的车牌
		// inputPlate_(val, old) {
		//   // 如果tmp被赋值,代表修改原有值
		//   this.tmp = old;
		//   this.license_ = val;
		//   // 贵C12345 和 贵C12345F 都为正常车牌,但是用户想输入 贵C12345F
		//   // this.inputPlateTmp_ = val;
		// },
		// inputPlateTmp_(val, old) {
		//   if (!val || val == old) { return }
		//   if (!val) { return }
		//   if (val && isVehicleNumber(val)) {
		//     this.inputPlate_ = val
		//     this.inputPlateTmp_ = "";
		//   }
		// }
	},
	beforeDestroy() {
		console.log('beforeDestroy');
		this.clear();
	},
	inActive() {},
	methods: {
		dealLicense(val) {
			let val_ = val.trim();
			if (!val_) {
				return;
			}
			// 避免删除一个车牌再输入相同的车牌不触发监听
			this.licenseObj_ = Object.assign({}, this.licenseObj_, {
				license_: val,
			});
		},
		setLicenseObj_(val) {
			this.licenseObj_ = Object.assign(
				{},
				{
					license_: val,
				},
			);
		},
		resetLicenseObj_() {
			this.licenseObj_ = Object.assign(
				{},
				{
					license_: '',
				},
			);
		},
		// 设置车牌号,但不update,否则可能死循环
		setLicenseListMap(val, needReset) {
			if (!val || (val instanceof Array && val.length == 0)) {
				this.clear();
				return;
			}
			val = val instanceof Array ? val : val.split(',');
			if (needReset) {
				this.licenseListMap.clear();
			}
			let max = this.limit ? parseInt(this.limit) : Infinity;
			val.forEach(ele => {
				if (this.licenseListMap.size < max) {
					// ele = ele.toUpperCase();
					this.licenseListMap.set(ele, ele);
				}
			});
			this.licenseList_ = [...this.licenseListMap.values()];
		},
		update() {
			this.licenseList_ = [...this.licenseListMap.values()];
			this.$emit('change', this.licenseList_);
			if (typeof this.value !== 'undefined') {
				let finalRes = this.value instanceof Array ? this.licenseList_ : this.licenseList_.join(',');
				this.$emit('update:value', finalRes);
			}
		},
		splitPlates(val) {
			//将车牌转换为数组中英文逗号隔开[,]
			if (typeof val !== 'string') {
				throw new Error('must be string');
			}
			let txtReg = new RegExp('；|：|’|“|，|《|。|》|、|？|-|·| |,|，|\\`|\\~|\\!|\\@|#|\\$|\\%|\\^|\\+|\\*|\\&|\\\\|\\/|\\?|\\||\\:|\\.|\\<|\\>|\\{|\\}|\\(|\\)|\\\'|\\;|\\=|"');
			val = val.split('');
			let arr = [];
			let strTmp_ = '';
			for (let i = 0; i < val.length; i++) {
				if (!txtReg.test(val[i])) {
					strTmp_ += val[i];
				} else {
					if (strTmp_) {
						arr.push(strTmp_);
					}
					strTmp_ = '';
				}
			}
			if (strTmp_) {
				arr.push(strTmp_);
			}
			return arr;
		},
		updateLicenseByInputKey(val) {
			// val = val.toUpperCase();
			if (!this.inputPlateTmp_) {
				return false;
			}
			if (!this.tmp && this.checkIsMax(true)) {
				return false;
			}
			let valArr = this.splitPlates(val);
			let errPlate = [];
			let succPlate = [];
			for (let i in valArr) {
				// 屏蔽车牌校验
				// if (!(isVehicleNumber(valArr[i]) || this.isWhiteList(valArr[i]))) {
				//   errPlate.push(valArr[i])
				// } else[
				//   succPlate.push(valArr[i])
				// ]
				if (this.isWhiteList(valArr[i])) {
					errPlate.push(valArr[i]);
				} else {
					succPlate.push(valArr[i]);
				}
			}
			if (succPlate.length == 0) {
				this.debounce(() => {
					this.$message.warning('车牌号验证错误:' + errPlate.join(','));
				});
				return false;
			}
			this.setLicenseObj_(succPlate.join(','));
			this.inputPlateTmp_ = '';
			return true;
		},
		checkIsMax(tip) {
			// 限制车牌数
			if (!isNaN(parseInt(this.limit))) {
				let len = parseInt(this.limit);
				if (this.licenseListMap.size >= len) {
					if (tip) {
						this.debounce(() => {
							this.$message.warning('不得超过当前最大输入车牌数:' + this.limit);
						});
					}
					return true;
				}
			}
			return false;
		},
		onPlateInputKeyDown(e) {
			// Enter 键
			if (e.which == 13 || e.key == 'Enter') {
				if (!this.inputPlateTmp_) {
					return;
				}
				if (this.updateLicenseByInputKey(this.inputPlateTmp_)) {
					this.inputPlateTmp_ = '';
				}
			}
		},
		onPlateInputBlur() {
			this.updateLicenseByInputKey(this.inputPlateTmp_);
		},
		onPlateInputSwitchClick() {
			if (this.disabled) {
				return;
			}
			this.keyboardOn = !this.keyboardOn;
		},
		// 删除某一个车牌
		onDeleteLicense(val) {
			// val = val.toUpperCase()
			if (this.disabled) {
				return;
			}
			this.licenseListMap.delete(val);
			this.update();
			this.tmp = '';
			this.inputPlateTmp_ = '';
			this.resetLicenseObj_();
		},
		// 改变已存在的车牌
		onChangeLicense(val) {
			// val = val.toUpperCase()
			if (this.disabled) {
				return;
			}
			if (this.isWhiteList(val)) {
				return;
			}
			// 修改新值前的旧值
			this.tmp = val;
			// 手动输入缓存值
			this.inputPlateTmp_ = val;
			this.disableInput = false;
			this.dispatchShowKeyboard(val);
		},
		dispatchShowKeyboard(val) {
			if (this.keyboardOn) {
				this.$refs['keyboard'].show(val);
			} else {
				this.$refs['customInput'].focus();
			}
		},
		onBoxInputClick() {
			if (this.disabled) {
				return;
			}
			if (!this.tmp && this.checkIsMax()) {
				return;
			}
			this.dispatchShowKeyboard('');
		},
		isWhiteList(plate) {
			return this.whiteList_.includes(plate);
		},
		clear() {
			this.licenseList_ = [];
			this.licenseListMap.clear();
			this.resetLicenseObj_();
			this.tmp = '';
			this.inputPlateTmp_ = '';
		},
	},
};
</script>

<style lang="scss" scoped>
.cus-close {
	cursor: pointer;
	font-size: 16px;
	margin: 0 0 0 4px;
	background-color: #fbfbfb;
	border-radius: 50%;
}

.bgActive {
	background-color: #ffffff;
}

.bgInactive {
	background-color: #f5f5f5;
}

.container {
	padding: 0;
	width: 100%;

	.box {
		position: relative;
		font-size: 14px;
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		position: relative;
		width: 100%;
		background-image: none;
		border-radius: 4px;
		border: 1px solid #dcdfe6;
		padding: 0 15px;

		box-sizing: border-box;

		.box_input {
			position: relative;
			// min-height: 40px;
			cursor: pointer;
			width: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			color: #606266;
			display: inline-block;
			font-size: inherit;
			outline: 0;
			transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

			.place_str {
				color: #ccc;
			}
		}

		.box_license {
			flex: 1;
			display: flex;
			flex-direction: row;
			align-items: center;
			box-sizing: border-box;
			flex-wrap: wrap;

			.box_license_item {
				& > span {
					cursor: text;
				}

				& > input {
					border: none;
					padding: 4px 0;
					width: 100%;
					height: 100%;
					background-color: transparent;

					&:focus {
						outline: none;
						border: none;
					}

					&:focus-visible {
						outline: none;
						border: none;
					}
				}

				&.box_license_item_input {
					border: none;
					padding: 2px 6px 2px 0;
					// width: 100%;
					flex: 1;
					min-width: 220px;
					height: 100%;
					background-color: transparent;
				}

				height: 24px;
				margin: 2px;
				border: solid 1px #e2e2e2;
				font-size: 12px;
				display: flex;
				align-items: center;
				padding: 0 10px;
				// margin: 5px;
				background-color: #f5f5f5;
				border-radius: 7px;
			}
		}

		.keyboard_flag {
			height: 20px;
			width: 20px;
		}

		.keyboard_on {
			background-repeat: no-repeat;
			background-image: url(../../../public/img/keyboard_on.png);
			background-size: contain;
		}

		.keyboard_off {
			background-repeat: no-repeat;
			background-image: url(../../../public/img/keyboard_off.png);
			background-size: contain;
		}
	}
}
</style>
