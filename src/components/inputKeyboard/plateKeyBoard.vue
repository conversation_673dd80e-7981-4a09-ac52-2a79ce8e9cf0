<template>
  <div>
    <div class="key_input">
      <el-tag :class="{'del_active':delIndex==index}" v-for="(tag,index) in tags" :key="tag" closable :type="tag.type" 
        @click="chooseEidtPlate(tag,index)" @close="delTags">
        {{tag}}
      </el-tag>
      <input ref="lyPlateInput" :type="inputStatus?'hidden':'text'" placeholder="请输入车牌" v-forbiddenFocus @focus="lyInputFocusFun" v-model="plate" class="ly_input" 
        @keyup.delete="keyDeleteFun" @keyup.enter="keyEnterConfirm" />
      <el-button class="control_board" :icon="[openKeyBoard?'el-icon-board':'el-icon-defult','el-icon-self']" @click="switchBoardStatus"></el-button>
    </div>
    <keyboard ref="keyboard" v-model="keyboardPlate" @confirm="keyBoardConfirm" />
  </div>
</template>
<script>
import keyboard from '@/components/keyboard/keyboardcopy'
import {isVehicleNumber} from '@/util/rules'
export default {
  name: "",
  components: { keyboard },
  
  props: {
    /**
     * 是否返回数组
     * 默认返回字符串 并默认以逗号 , 分隔
    */ 
    backIsArray:{
      default: false,
    },
    /*
    * 返回字符串的分隔符 
    * 默认是逗号 , 
    */ 
    splitStr:{
      type:String,
      default: ",",
    },
    /*
    *多车牌输入控制
    * 默认单车牌输入
    * type Boolean
    * default false  
    */
    multiple: {
      default: false,
    },
    /*
    * 车牌是否验证
    *
    */
    validated:{
      type:Boolean,
      default:true
    },
    // 是否开启虚拟键盘 sync
    openKeyBoard:{
      type:Boolean,
      default:false
    },
    /*
    * 车牌
    */ 
    value: [String, Array],
  },
  watch:{
    value(val){
        console.log(val)
    },
    plate(val){
      this.delIndex = -1
    },
    // 输入得车牌序列
    tags(val){
      let newDifVal = val
      if(!this.backIsArray){
        newDifVal = val.join(this.splitStr)
      }
      this.value = newDifVal
      this.$emit('input', this.value)
    },
    openKeyBoard(val){
      console.log("openKeyBoard",val)
    }
  },
  computed:{
    inputStatus(){
      return !this.multiple && this.tags.length==1
    }
  },
  data() {
    return {
      plate: "",
      tags: [],
      keyboardPlate: '',
      delIndex:-1,
      
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    init(){
      // this.openKeyBoard && this.open();
      if (this.multiple) {
        this.manyPlateDeal(this.value)

      } else {
        this.value && this.tags.push(this.value);
      }
    },


    addTags(v) {
      
      let repeatPlate = this.tags.filter(item=>item===v)
      // 空值不插入
      if(v===''){
        this.plate = "";
        return
      }
      if(repeatPlate.length!=0){
        this.$message.error("该车牌已存在")
        return
      }
      this.tags.push(v);
      this.plate = "";
    },

    // 车牌值拆分
    plateSplitVal(){

    },

    // 打开键盘
    open() {
      this.$refs['keyboard'].show();
    },

    // 虚拟键盘开关
    switchBoardStatus(){
      this.openKeyBoard = !this.openKeyBoard
      console.log('this.openKeyBoard',this.openKeyBoard)
      this.$emit('update:openKeyBoard',this.openKeyBoard);
      this.openKeyBoard && this.open();
    },
    
    demo() {

    },

    // 当输入框获取到焦点得时候
    lyInputFocusFun(){
      if(this.openKeyBoard){
        this.$refs.lyPlateInput.blur()
        this.open()
      }
    },
    // 虚拟键盘得确认事件
    keyBoardConfirm() {
      this.addTags(this.keyboardPlate);
    },

    // 回车键
    keyEnterConfirm(){
      let strPlate = this.plate
      if(this.plate.trim()==="")return
      // 若开启车牌验证
      if(this.validated){
        // 屏蔽车牌校验
        // if(!isVehicleNumber(strPlate)){
        //   // 验证不通过
        //   this.$message.error("请输入正确的车牌号")
        //   return;
        // }
      }
      // this.openKeyBoard = false
      console.log('keyEnterConfirm',this.openKeyBoard)
      this.$emit('update:openKeyBoard', this.openKeyBoard);
      this.addTags(this.plate)
      this.$emit('input', this.value)
    },

    // 返回键  删除
    keyDeleteFun(){
      // 输入框没值且索引不等于-1 执行删除
      if(this.plate.length===0 && this.delIndex!=-1){
        this.tags.pop()
        this.delIndex = -1
      }
      if(this.plate.length===0){
        this.delIndex= this.tags.length - 1
      }
    },
    // 点击删除tag
    delTags(tag) {
      this.tags.splice(this.tags.indexOf(tag), 1);
    },

    // 选择并编辑
    chooseEidtPlate(val){
      if(!this.openKeyBoard){
        this.plate = val
        this.delTags(val)
        !this.inputStatus && this.$refs.lyPlateInput.focus()

      }else{
        this.keyboardPlate = val
        this.delTags(val)
        this.open()
      }
    },
    
    // 多车牌返回
    manyPlateDeal(mulPlate){
      // 分为数组 和 以逗号隔开得字符串
      if(Array.isArray(mulPlate)){
        this.tags.push(...mulPlate);
      }

      if(typeof(mulPlate)=='string'){
        let arryPlate = mulPlate.split(',')
        this.tags.push(...arryPlate);
      }
    },


    // 车牌验证是否通过
    plateValidateResult(val){
       return isVehicleNumber(val)
    },

    

  },
}
</script>
<style scoped lang="scss">
/deep/ .el-tag {
  height: 24px;
  line-height: 22px;
  margin-right: 10px;
  margin-bottom: 2px;
  margin-top: 2px;
  &.del_active{
    border-color:brown;
  }
}


/deep/ .el-icon-self{
  background-repeat: no-repeat;
  background-position: center right;
  font-size: 22px;
  background-size: cover;
  vertical-align: middle;
}
/deep/ .el-icon-self:before{
    content: "替";
    font-size: 20;
    color:transparent;
}
/deep/.el-icon-board{
  background-image: url(data:image/png;base64,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);
}
/deep/ .el-icon-defult{
  background-image: url(data:image/png;base64,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);
}
</style>
<style scoped>
.key_input {
  box-sizing: border-box;
  position: relative;
  padding: 0;
  margin-left: 15px;
  color: #666;
  font-size: 14px;
  appearance: none;
  min-height: 30px;
  background-color: transparent;
  border: 1px solid #dcdfe6;
  display: flex;
  align-items: center;
flex-wrap: wrap;
justify-content: space-between;
  padding: 0px 10px;
  padding-right: 60px;
}
.control_board{
  position: absolute;
  right: 0;
  top:0;
  /* bottom: 0;     */
  border: 0 none;
  min-height: 28px;
  padding: 0px 20px;
}
.ly_input {
  border: none;
  outline: none;
  height: 26px;
  line-height: 26px;
  flex: 1;
  min-width: 200px;
}


</style>