<template>
    <div @click="click" style="display: inline-block; margin-top: -2px; line-height: 18px;text-align: center">
      <i :style="{fontSize: size+'px'}" v-if="src&&src.startsWith('icon')" :class="src"></i>
      <el-image :style="{ width:size+'px', height:size+'px', marginRight:10+'px' }" v-else :src="src">
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
      </el-image>
    </div>
</template>
<script>

export default {
  name: 'menu-icon',
  props: {
    src: String,
    showName: Boolean,
    size: {
      type: String,
      default: 14
    },
  },
  data() {
    return {
    };
  },
  watch: {
    'size'() {
      console.log('----size----', this.size);
    },
  },
  methods: {
    click() {
      this.$emit("click", this.src)
    }
  }

};
</script>
<style lang="scss" scoped>
  .icon {
    width: 18px;
    height: 18px;
    margin-right: 10px;
  }
</style>
