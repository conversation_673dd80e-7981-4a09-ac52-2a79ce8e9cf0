<template>
<div>
    
<!-- 
    :before-remove="beforeRemove" 'upload-demo'
    :limit="defaultOption.limit" -->
  <el-upload
    :class="['upload-demo' ,{'has_video':!fileIsPic}]"
    drag
    :limit="limitNumber"
    :multiple="multiple"
    :on-change="handleChange"
    :on-preview="handlePreview"
    :on-remove="handleRemove"
    :on-exceed="handleExceed"
	:http-request="afterUpload"
    :before-upload="beforeUpload"
    list-type="picture"
    :file-list="fileList">
    <i class="el-icon-upload"></i>
    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    <!-- <el-button size="small" type="primary">点击上传</el-button> -->
    <div slot="tip" class="el-upload__tip">{{tip}}</div>
    </el-upload>
    <!-- <div v-if="fileIsPic">
        <video :key="index" width="320" height="240" controls>
            <source  :src="fileList" type="video/mp4">
            <source  :src="fileList"  type="video/ogg">
            您的浏览器不支持 HTML5 video 标签。
        </video>
    </div> -->
</div>
</template>
<script>
import {uploadFlie} from '@/api/public';
  export default {
    props:{
        uploadFile: String,
        ids:String,
        option:{
            type:Object,
            default:()=>{}
        },
        isPicture:{
            typeof:Boolean,
            default:true
        },
        limitNumber:{
            typeof: Number,
            default:1
        },
        multiple:{
            typeof:Boolean,
            default:false
        },
        tip:{
            type:String,
            default:'支持扩展名：.rar .zip .doc .docx .pdf .jpg...'
        },
        limitFileSize:{
            typeof: Number,
            default:10
        }
    },
    watch:{
        option(val){
            if(typeof val == 'object'){
              this.defaultOption = Object.assign({},this.defaultOption,val)  
            }
        },
        uploadFile: {
            handler(val, olVal){
                this.fileList = []
                if(val){
                    
                    this.fileList = [{
                        url:val
                    }]
                }
                this.fileIsPic = this.isPicture
                console.log('我变化了', val, olVal)
            },
            deep: true
        },
        isPicture(){
            // this.fileList = []
        }
    },
    data() {
        return {
            fileIsPic:false,
            defaultOption:{
                multiple:true,
            },
            headers:"",
            loading:null,
            
            fileList: []
        };
    },
    methods: {
        beforeRemove(file, fileList) {
            return this.$confirm(`确定移除 ${ file.name }？`);
        },
        handleRemove(file, fileList) {
            this.updateList(fileList)
        },
        handlePreview(file) {

        },
        uploadSuccess( file, fileList){

        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 ${this.limitNumber} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
        },
        handleChange(file, fileList) {
            // this.fileList = fileList.slice(0,this.limitNumber);
        },
        beforeUpload(file) {
			const isJPG = file.type === 'image/jpeg'||file.type==='image/png'||file.type==='image/jpg'||file.type==='video/mp4';
            
			if (!isJPG) {
				this.$message.error('上传图片/视频只能是 jpg,jpeg,png,mp4】 格式!');
                return isJPG
			}
            
            this.loading = this.$loading({
                lock: true,
                text: '文件上传中',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            let type = file.type
            this.fileIsPic = type == "video/mp4"?false:true

            

            if(this.fileIsPic != this.isPicture){
                this.$message.error(`上传对应的文件类型`);
                this.loading.close()
                return false
            }
            const isLimit = file.size / 1024 / 1024 < this.limitFileSize;
            if (!isLimit) {
                this.$message.error(`上传文件大小不能超过 ${this.limitFileSize}MB!`);
            }
            return isLimit;

        },
        updateList(fileList){
            let str = null
            try {
                str = fileList.map(item=>item.link)
            } catch (error) {
                str = []
                this.fileList = []
            }
            this.$emit("update:ids",str.join(','))
            this.$emit("update:uploadFile",str.join(','))
            
        },
      
        // 上传流程
        afterUpload(object){

            let file = object.file;

            uploadFlie(file).then((result) => {  
                console.log(result)
                if(result.data.code == 200){
                    this.$message.success(`上传成功`);
                }
                let resData = result.data.data
                let picList = {
                    'name' : resData.originalName,
                    'url' : resData.link
                }
                resData = Object.assign(resData,picList)
                this.fileList.push(resData)
                this.updateList(this.fileList)
                this.loading.close()
            }).catch(() => {
                // this.updateList([])
                this.loading.close()
            });

        },
    }
  }
</script>
<style lang="scss" scoped>
    /deep/.el-upload-list{
        >li{
            width: 48%;
        }
    }
    .has_video{
        
        /deep/.el-upload-list{
            >li{
                display: inline-block;
                min-width: 270px;
                width: 48%;
                margin-right:6px;
                padding: 10px 10px 10px 10px;
            }
            .el-upload-list__item-name{
                margin-left:90px;
            }
        }
    }
    .upload-demo{
        border: 1px solid #DCDFE6;
        padding:10px;
    }
    // /deep/.el-upload-list--picture .el-upload-list__item.is-success .el-upload-list__item-name i{
    //     display: block !important;
    // }
    // /deep/.el-upload-list__item{
    //     transition: none;
    // }
</style>