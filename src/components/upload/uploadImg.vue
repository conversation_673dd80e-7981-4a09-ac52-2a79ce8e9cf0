<!--  -->
<template>
  <div>
    <el-upload
      class="avatar-uploader"
        :action="uploadFileUrl()"
      :show-file-list="false"
      :headers="postHeader"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
      :upload-after="uploadAfter"
    >
      <img v-if="imageUrl" :src="imageUrl" class="avatar" />
      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    </el-upload>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  props: {
    imageUrl: {
      type: String,
      default: function() {
        return "";
      }
    }
  },
  data() {
    return {
      submitForm: {}
    };
  },
  computed: {
    ...mapGetters(["permission", "token"]),
    postHeader() {
      return {
        "Blade-Auth": "bearer " + this.token
      };
    }
  },
  methods: {
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isPNG = file.type === "image/png";
      const isGIF = file.type === "image/gif";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!(isJPG || isPNG || isGIF)) {
        this.$message.error("上传图片只能是 JPG/PNG/GIF格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return (isJPG || isPNG || isGIF) && isLt2M;
    },
    handleAvatarSuccess(res) {
      this.imageUrl = res.data.link;
      this.$emit("getImgUrl", this.imageUrl);
    },

    created() {},
    mounted() {}
  }
};
</script>
<style lang="scss" scoped>
/deep/ .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
