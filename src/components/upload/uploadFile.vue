<template>
  <div class="fileContainer">
    <div>
      <el-upload ref="upload" :headers="postHeader" class="avatar-uploader" :on-preview="handlePreview"
        :on-remove="handleRemove" :action="uploadFileUrl()" :show-file-list="showList" :file-list="fileList"
        :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-exceed="handleExceed" multiple
        :before-upload="beforeUpload">
        <div class="handleUpload">
          <el-button type="primary">点击上传</el-button>
          <div class="handleUploadTip">{{ uploadTip }}</div>
        </div>
      </el-upload>
    </div>
    <div class="stepPersonDetail" v-if="!showList">
      <div class="stepPersonDetailC">
        <div class="detailCAttach">
          <div class="attachImg">
            <div class="attachImgList" v-for="(val, index) in showImgs" v-bind:key="index">
              <div class="attachImgInner">
                <el-image style="width: 100px; height: 100px; margin: 5px" :src="val" :preview-src-list="showImgs">
                </el-image>
                <i class="
                    el-icon-circle-close
                    attachFileIcon
                    close
                    attachImgInnerClose
                  " @click.stop="
                    handleRemove(val.slice(val.lastIndexOf('/') + 1))
                  "></i>
              </div>
            </div>
          </div>
          <div class="attachFile">
            <div class="attachFileList" v-for="(val, index) in showFiles" v-bind:key="index">
              <div class="fileListInner">
                <i class="el-icon-document attachFileIcon"></i>
                <a class="attachFileText" :href="val">{{
                    val.slice(val.lastIndexOf("/") + 1)
                }}</a>
                <i class="
                    el-icon-circle-close
                    attachFileIcon
                    attachFileIconClose
                    close
                  " @click.stop="
                    handleRemove(val.slice(val.lastIndexOf('/') + 1))
                  "></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ShowImg :imgVisible.sync="imgVisible" :imgSrc.sync="imgSrc"></ShowImg>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import ShowImg from "../../views/approval/approvalCenter/components/showImg.vue";
/**
 * limitFileNum 文件数量
 * limitFileSize 文件大小 Mb
 * fileType 文件类型，默认上传图片，可额外添加,[逗号隔开]
 * files.sync 已上传的文件URL列表
 */
export default {
  name: "upload",
  components: {
    ShowImg,
  },
  props: {
    showList: {
      type: Boolean,
      require: false,
      default: false,
    },
    limitFileNum: {
      type: Number,
      require: false,
      default: 6,
    },
    limitFileSize: {
      type: Number,
      require: false,
      default: 20,
    },
    fileType: {
      type: String,
      require: false,
      default: "",
    },
    files: {
      type: Array,
      require: false,
    },
  },
  created() {
    this.init()
  },
  data() {
    return {
      imgFileType: [
        // 用于展示错误提示信息:匹配 .jpg jpg jpeg .jpeg image/jpg image/jpeg
        ["jpeg", /(^image\/jpe?g$)|^\.?jpe?g$/],
        ["png", /(^image\/png$)|^\.?png$/],
        ["gif", /(^image\/gif$)|^\.?gif$/],
      ],
      defaultFileType: new Map(),
      filterFiles: new Map(),
      filterImgs: new Map(),
      showImgs: [],
      showFiles: [],
      imgSrc: "",
      imgVisible: false,
      fileList: [],
      uploadLoading: null,
    };
  },
  watch: {
    fileType(val) {
      this.mergePropsFileType(val);
    },
    files(val) {
      if (val) {
        this.init()
      }
    }
  },
  computed: {
    ...mapGetters(["token"]),
    postHeader() {
      return {
        "Blade-Auth": "bearer " + this.token,
      };
    },

    uploadTip() {
      return `可上传 ${[...this.defaultFileType.keys()].join(
        ","
      )}文件!单个文件不超过${this.limitFileSize}MB,最多${this.limitFileNum
        }个文件`;
    },
  },
  methods: {
    init() {
      this.imgFileType.forEach((val) => {
        this.defaultFileType.set(val[0], val[1]);
      });
      this.limitFileNum = parseInt(this.limitFileNum);
      this.limitFileSize = parseInt(this.limitFileSize);
      this.mergePropsFileType(this.fileType);
      this.setInboundFiles();

    },
    // 通过files.sync传进来的文件URL列表
    setInboundFiles() {
      for (let i in this.files) {
        let url_ = this.files[i];
        this.updateUploadedFiles(
          url_.slice(url_.lastIndexOf("/") + 1),
          url_,
          true
        );
      }
    },
    mergePropsFileType(val) {
      try {
        let val_ = val.split(",");
        val_.forEach((val) => {
          if (val.length > 0) {
            this.defaultFileType.set(val, new RegExp(val));
          }
        });
      } catch (e) {
        console.error("fileType error ==>", e);
      }
    },
    isImg_(name) {
      name = name.slice(name.lastIndexOf("."));
      name = name.toLowerCase();
      for (let i in this.imgFileType) {
        if (this.imgFileType[i][1].test(name)) {
          return true;
        }
      }
      return false;
    },
    isSpecifyFileType_(name) {
      name = name.slice(name.lastIndexOf("."));
      name = name.toLowerCase();
      for (let i of this.defaultFileType.values()) {
        if (i.test(name)) {
          return true;
        }
      }
      return false;
    },
    /**
     * 如果reverse为true，表示上传的文件URL是从外部files参数传入进来的，用于回恢复当前上传状态，
     * 这时不应该修改files对象本身
     */
    updateUploadedFiles(name, url, reverse) {
      // 有则改变，无则添加，url为空为删除
      let chooseMap = null;
      if (this.isImg_(name)) {
        chooseMap = this.filterImgs;
      } else {
        chooseMap = this.filterFiles;
      }
      let isAdd = null;
      if (chooseMap.has(name)) {
        isAdd = url ? true : false;
      } else {
        if (url) {
          isAdd = true;
        }
      }
      if (isAdd != null) {
        if (isAdd) {
          if (reverse || this.files.length < this.limitFileNum) {
            chooseMap.set(name, url);
          }
        } else {
          chooseMap.delete(name);
        }
      }
      this.showImgs = [...this.filterImgs.values()];
      this.showFiles = [...this.filterFiles.values()];
      if (!reverse) {
        this.$emit("update:files", this.showImgs.concat(this.showFiles));
      }
    },
    showImg(url) {
      this.imgSrc = url;
      this.imgVisible = true;
    },
    handlePreview(file) {
      if (this.isImg_(file.name)) {
        var reader = new FileReader();
        reader.readAsDataURL(file.raw);
        reader.onload = (event) => {
          this.showImg(event.target.result);
        };
      } else {
        //todo 本地上传的文件
      }
    },
    handleRemove(file) {
      file = typeof file == "string" ? file : file.name;
      this.updateUploadedFiles(file);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${this.limitFileNum} 个文件，本次选择了 ${files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      );
    },
    beforeUpload(file) {
      // 不能超过文件数目限制
      if (this.files.length >= this.limitFileNum) {
        return false;
      }
      this.uploadLoading = this.$loading({
        // lock: true,
        text: `文件上传中,请稍后。。。`,
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.8)",
      });
      let isType = this.isSpecifyFileType_(file.name);
      const isLimit = file.size / 1024 / 1024 < this.limitFileSize;
      if (!isType) {
        this.$message.error(
          `上传文件只能是 ${[...this.defaultFileType.keys()].join(",")}格式!`
        );
      }
      if (!isLimit) {
        this.$message.error(`上传文件大小不能超过 ${this.limitFileSize}MB!`);
      }
      this.uploadLoading.close();
      return isType && isLimit;
    },
    handleUploadSuccess(res) {
      this.uploadLoading && this.uploadLoading.close();
      let name = res.data.name.slice(res.data.name.lastIndexOf("/") + 1);
      // let oFiles = this.$refs.upload.uploadFiles;
      // for (let i in oFiles) {
      //   if (oFiles[i].name == res.data.originalName) {
      //     oFiles[i].name = name;
      //     break;
      //   }
      // }
      this.updateUploadedFiles(name, res.data.link);
    },
    // 上传失败
    handleUploadError(err, file, fileList) {
      console.log(err.type);
      this.uploadLoading && this.uploadLoading.close();
      this.$message({
        type: "error",
        message: "上传失败",
      });
    },
    splitUrl(url) {
      return url.substr(url.lastIndexOf("/") + 1);
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .avatar-uploader .el-upload {
  border: none;
}

.close {
  color: #f56c6c;
}

.fileContainer {
  display: flex;
  flex-direction: column;

  .handleUpload {
    display: flex;
    align-items: center;

    .handleUploadTip {
      text-align: left;
      cursor: auto;
      line-height: 1.2rem;
      margin: 0 0 0 0.5rem;
    }
  }

  .stepPersonDetail {
    display: flex;
    flex-direction: column;

    .stepPersonDetailC {
      color: #303133;
      margin: 0.3rem 0;

      .detailCKey {
        margin: 0 0.7rem 0 0;
        float: left;
        overflow: hidden;
      }

      .detailCvalue,
      .detailCAttach {
        overflow: hidden;
      }

      .detailCAttach {
        .attachFileIcon {
          cursor: pointer;
          margin: 0 5px;
        }

        .attachFileText {
          text-overflow: ellipsis;
          box-sizing: border-box;
          white-space: nowrap;
          overflow: hidden;
          padding: 0 27px 0 0;
        }

        .attachFileIconClose {
          position: absolute;
          right: 0;
        }

        .attachImg {
          &::after {
            content: " ";
            display: table;
            clear: both;
          }

          .attachImgInner {
            position: relative;
          }

          .attachImgList {
            float: left;
          }

          .attachImgInnerClose {
            position: absolute;
            right: -3px;
            top: 3px;
            background-color: #fff;
            font-size: 1.2rem;
            border-radius: 50%;
          }
        }

        .attachFile {
          width: 100%;
          display: block;

          &::after {
            clear: both;
            content: " ";
            display: table;
          }
        }

        .attachFileList {
          font-size: 1rem;
          display: flex;
          // flex-direction: column;
          // align-items: center;
          // align-items: flex-start;
          float: left;
          box-sizing: border-box;
          position: relative;
          margin: 3px 0;
          width: 50%;

          .fileListInner {
            border: dashed 1px #ccc;
            position: relative;
            width: 90%;
            display: flex;
            flex-direction: row;
            align-items: center;
          }
        }

        // .attachImg {
        // }
        // .attachImgList {
        // }
      }
    }
  }
}
</style>
