<template>
  <el-dialog
    title="图片预览"
    :visible.sync="imgVisible"
    width="80%"
    top="5vh"
    :before-close="imgDlgClose"
    append-to-body
  >
    <div class="contentContainer">
      <el-image
        style="width: 50%; height: auto"
        :src="imgSrc"
        fit="contain"
      ></el-image>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "showImg",
  props: {
    imgVisible: false,
    imgSrc: "",
  },
  data() {
    return {};
  },
  methods: {
    showImg(url) {
      this.imgSrc = url;
      this.imgVisible = true;
    },
    imgDlgClose() {
      this.imgVisible = false;
      this.$emit("update:imgVisible", false);
      this.$emit("update:imgSrc", "");
    },
  },
};
</script>

<style scoped>
.contentContainer {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
}
</style>
