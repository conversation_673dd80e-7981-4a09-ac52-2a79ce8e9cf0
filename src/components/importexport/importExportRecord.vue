<template>
	<el-drawer :title="importVal ? '导入记录' : '导出记录'" :visible.sync="visible" direction="rtl" :size="importVal ? '70%' : '60%'" :modal="false">
		<span class="tips" v-if="!importVal">注：请等待状态为已完成时才可以下载</span>
		<section class="form-container">
			<avue-crud
				ref="crud"
				:option="option"
				:data="data"
				:page="page"
				@search-change="searchChange"
				@search-reset="searchReset"
				@current-change="currentChange"
				@size-change="sizeChange"
				@refresh-change="refreshChange">
				<template slot="fileName" slot-scope="scope">
					<el-link type="primary" @click="downloadFile(scope.row)">{{ scope.row.fileName }}</el-link>
				</template>
				<template slot="status" slot-scope="scope">
					<el-tag :type="getStatusType(scope.row.status)" effect="dark">{{ getStatusLabel(scope.row.status) }}</el-tag>
				</template>
				<template slot="menu" slot-scope="scope">
					<el-button type="text" size="small" v-if="scope.row.fileLink" @click="downloadFile(scope.row)">下载</el-button>
					<el-button type="text" size="small" v-if="scope.row.operationType == 1" @click="showDetail(scope.row)">查看明细</el-button>
				</template>
			</avue-crud>
		</section>
		<importExportRecordInfo ref="importExportRecordInfoRef" :serviceId="serviceIdMap[serviceId]"></importExportRecordInfo>
	</el-drawer>
</template>

<script>
import { requestExport as requestExportApi, importExportRecordPage } from '@/api/public';
import { uploadFileUrl } from '@/util/processUrlReplace';
import { getToken } from '@/util/auth';
import importExportRecordInfo from './importExportRecordInfo';

export default {
	name: 'importExportRecord',
	components: {
		importExportRecordInfo,
	},
	props: {
		importVal: {
			type: Boolean,
			default: false,
		},
		businessType: {
			type: String,
			required: true,
		},
		serviceId: {
			type: String,
			required: true,
			default: 'park',
		},
	},
	data() {
		return {
			serviceIdMap: {
				park: 'lecent-park',
				report: 'blade-report',
			},
			visible: false,
			query: {},
			page: {
				pageSize: 15,
				currentPage: 1,
				total: 0,
			},
			option: {
				height: 'auto',
				calcHeight: 80,
				menuWidth: this.importVal ? 200 : 120,
				tip: false,
				searchShow: true,
				searchMenuSpan: 6,
				border: true,
				index: true,
				viewBtn: false,
				editBtn: false,
				delBtn: false,
				addBtn: false,
				column: [
					{
						label: '操作时间',
						prop: 'operationTime',
					},
					{
						label: '操作人',
						prop: 'operatorName',
					},
					{
						label: '文件名',
						prop: 'fileName',
						slot: true,
					},
					{
						label: '总记录数',
						prop: 'totalNum',
						hide: !this.importVal,
						width: 80,
					},
					{
						label: '成功记录数',
						prop: 'successNum',
						hide: !this.importVal,
						width: 90,
					},
					{
						label: '失败记录数',
						prop: 'failNum',
						hide: !this.importVal,
						width: 90,
					},
					{
						label: '耗时',
						prop: 'duration',
						width: 100,
						formatter: function (row, value, column) {
							if (value == null || value <= 0) {
								return '--';
							}
							if (value < 1000) {
								return value + ' 毫秒';
							}
							return (value / 1000).toFixed(2) + ' 秒';
						},
					},
					{
						label: '状态',
						prop: 'status',
						type: 'select',
						search: true,
						width: 100,
						dicData: [
							{
								label: '待开始',
								value: 1,
							},
							{
								label: '进行中',
								value: 2,
							},
							{
								label: '已完成',
								value: 3,
							},
							{
								label: '部分失败',
								value: 4,
							},
							{
								label: '全部失败',
								value: 5,
							},
						],
						slot: true,
					},
					{
						label: '失败原因',
						prop: 'failReason',
						overHidden: true,
					},
				],
			},
			data: [],
		};
	},
	computed: {
		getStatusLabel() {
			return status => {
				const labels = {
					1: '待开始',
					2: '进行中',
					3: '已完成',
					4: '部分失败',
					5: '全部失败',
				};
				return labels[status] || '未知状态';
			};
		},
		getStatusType() {
			return status => {
				const types = {
					1: 'info',
					2: 'warning',
					3: 'success',
					4: 'danger',
					5: 'danger',
				};
				return types[status] || 'default';
			};
		},
	},
	methods: {
		requestExport(params) {
			// 添加加载层
			this.$loading({
				lock: true,
				text: '正在请求导出中...',
				spinner: 'el-icon-loading',
				background: 'rgba(0, 0, 0, 0.7)',
			});
			const serId = this.getServiceId(this.serviceId);
			requestExportApi(serId, this.businessType, params).then(res => {
				this.$loading().close();
				this.$message({ type: 'success', message: '导出中，请在导出记录中查看' });
				this.show();
			}).catch(() => {
				this.$loading().close();
			});
		},

		showDetail(row) {
			this.$refs.importExportRecordInfoRef.show(row.id);
		},
		show() {
			this.visible = true;
			this.refreshChange();
		},
		searchReset() {
			this.query = {};
			this.onLoad(this.page);
		},
		searchChange(params, done) {
			this.query = params;
			this.page.currentPage = 1;
			this.onLoad(this.page, params);
			done();
		},
		currentChange(currentPage) {
			this.page.currentPage = currentPage;
			this.onLoad(this.page);
		},
		sizeChange(pageSize) {
			this.page.pageSize = pageSize;
			this.onLoad(this.page);
		},
		refreshChange() {
			this.onLoad(this.page);
		},
		onLoad(page) {
			var serId = this.getServiceId(this.serviceId);
			this.query.businessType = this.businessType;
			importExportRecordPage(serId, page.currentPage, page.pageSize, this.query).then(res => {
				const data = res.data.data;
				this.page.total = data.total;
				this.data = data.records;
			});
		},
		downloadFile(row) {
			if (row.fileLink) {
				var serId = this.getServiceId(this.serviceId);
				const url = uploadFileUrl(`/api/${serId}/importExport/download?recordId=${row.id}&Blade-Auth=${getToken()}`);
				window.open(url);
			}
		},
		getServiceId(serId) {
			return this.serviceIdMap[this.serviceId];
		},
	},
};
</script>

<style scoped lang="scss">
.form-container {
	overflow: auto;
	height: 100%;
	padding: 30px 20px;
}

.file-link {
	color: #3995fe;
	cursor: pointer;
	text-decoration: none;
	transition: text-decoration 0.3s ease;
	&:hover {
		text-decoration: underline;
	}
}

.el-button--text {
	color: #409eff;
	font-size: 14px;
	padding: 0;
}

/deep/.el-card__body {
	padding: 20px !important;
}

/deep/.el-table th {
	background-color: #f5f7fa;
	color: #303133;
	font-weight: bold;
	padding: 10px 0;
}

/deep/.el-table td {
	border-bottom: 1px solid #ebeef5;
	padding: 8px 0;
}

/deep/.el-pagination {
	margin-top: 30px;
}
.tips {
	color: red;
	padding: 0 20px;
}
</style>