<template>
	<el-drawer title="导入明细" :visible.sync="visible" direction="rtl" size="50%" :modal="false">
		<div class="form-container">
			<avue-crud
				ref="crud"
				:option="option"
				:data="data"
				:page="page"
				@search-change="searchChange"
				@search-reset="searchReset"
				@current-change="currentChange"
				@size-change="sizeChange"
				@refresh-change="refreshChange"></avue-crud>
		</div>
	</el-drawer>
</template>

<script>
import { importExportRecordInfoPage } from '@/api/public';
export default {
	name: 'importExportRecordInfo',
	props: {
		serviceId: {
			type: String,
			require: true,
		},
	},
	data() {
		return {
			loading: false,
			data: [],
			visible: false,
			id: '',
			page: {
				currentPage: 1,
				pageSize: 10,
				total: 0,
			},
			searchForm: {
				recordId: '',
			},
			option: {
				height: 'auto',
				calcHeight: 80,
				tip: false,
				searchShow: true,
				searchMenuSpan: 6,
				border: true,
				index: true,
				viewBtn: false,
				editBtn: false,
				delBtn: false,
				addBtn: false,
				menu: false,
				column: [
					{
						label: '类型',
						prop: 'success',
						formatter: row => {
							return row.success ? '成功' : '失败';
						},
						width: 60,
					},
					{
						label: '数据所在excel文档行数',
						prop: 'rowNum',
						width: 150,
					},
					{
						label: 'excel文档数据行json原数据',
						prop: 'rowData',
						minWidth: 100,
						width: 700,
						overHidden:true,
					},
					{
						label: '失败原因',
						prop: 'failReason'
					},
				],
			},
		};
	},
	methods: {
		show(id) {
			this.visible = true;
			this.id = id;
			this.onLoad(this.page);
		},
		searchChange(params, done) {
			this.searchForm = params;
			this.page.currentPage = 1;
			this.onLoad(this.page, params);
			done();
		},
		searchReset() {
			this.searchForm = {};
			this.onLoad(this.page);
		},
		currentChange(currentPage) {
			this.page.currentPage = currentPage;
			this.onLoad(this.page);
		},
		sizeChange(pageSize) {
			this.page.pageSize = pageSize;
			this.onLoad(this.page);
		},
		onLoad(page) {
			this.searchForm.recordId = this.id;
			importExportRecordInfoPage(this.serviceId, page.currentPage, page.pageSize, this.searchForm).then(res => {
				this.data = res.data.data.records;
				this.page.total = res.data.data.total;
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.el-drawer__body {
	padding: 20px;
}
</style>