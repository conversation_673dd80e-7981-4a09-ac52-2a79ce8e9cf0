import Mock from 'mockjs'

const Random = Mock.Random

// 生成元数据列表
function getMetadataList() {
  const data = []
  const categories = [
    'COLLECTION_BUSINESS',
    'PARKING_BUSINESS', 
    'PAYMENT_BUSINESS',
    'SYSTEM_BUSINESS'
  ]
  
  // 为每个分类生成不同数量的数据
  categories.forEach((category, index) => {
    const count = 3 + index * 2; // 每个分类生成不同数量的数据
    for (let i = 0; i < count; i++) {
      data.push({
        id: Random.id(),
        businessCategory: category,
        paramLevel: Random.pick(['1', '2']),
        paramKey: category.toLowerCase() + '.' + Random.word(3, 8),
        paramName: Random.ctitle(4, 8),
        paramType: Random.pick(['STRING', 'NUMBER', 'BOOLEAN', 'DATE', 'JSON']),
        defaultValue: Random.pick(['30', 'true', 'false', '2024-01-01', '{"key":"value"}']),
        validationRule: Random.pick(['{"min":1,"max":365}', '{"pattern":"^[0-9]+$"}', '{"required":true}']),
        description: Random.csentence(10, 20),
        isRequired: Random.pick(['0', '1']),
        displayOrder: Random.integer(1, 100),
        version: Random.integer(1, 10),
        createTime: Random.datetime(),
        updateTime: Random.datetime()
      })
    }
  })
  return data
}

// 生成参数值列表
function getValueList() {
  const data = []
  for (let i = 0; i < 15; i++) {
    data.push({
      id: Random.id(),
      paramKey: Random.word(5, 10) + '.' + Random.word(3, 8),
      paramName: Random.ctitle(4, 8),
      paramValue: Random.pick(['30', 'true', 'false', '2024-01-01', '{"key":"value"}', 'test value']),
      tenantId: Random.pick(['', 'tenant001', 'tenant002']),
      appId: Random.pick(['', 'app001', 'app002']),
      remark: Random.csentence(5, 15),
      createTime: Random.datetime(),
      updateTime: Random.datetime()
    })
  }
  return data
}

// 模拟分页数据
function getFakeList(params) {
  const { current = 1, size = 10, businessCategory } = params
  let list = params.type === 'metadata' ? getMetadataList() : getValueList()
  
  // 如果指定了业务分类，进行筛选
  if (businessCategory && businessCategory !== 'root') {
    list = list.filter(item => item.businessCategory === businessCategory)
  }
  
  const start = (current - 1) * size
  const end = start + size
  const pageList = list.slice(start, end)
  
  return {
    code: 200,
    msg: '操作成功',
    data: {
      records: pageList,
      total: list.length,
      size: size,
      current: current,
      pages: Math.ceil(list.length / size)
    }
  }
}

function fakeSuccess() {
  return {
    code: 200,
    msg: '操作成功'
  }
}

export default ({mock}) => {
  if (!mock) return;
  
  Mock.mock(/\/api\/blade-system\/business-param\/metas\/page/, 'get', (options) => {
    const params = new URLSearchParams(options.url.split('?')[1])
    return getFakeList({
      current: params.get('current') || 1,
      size: params.get('size') || 10,
      businessCategory: params.get('businessCategory') || '',
      type: 'metadata'
    })
  });
  
  Mock.mock(/\/api\/blade-system\/business-param\/metadata\/submit/, 'post', fakeSuccess);
  Mock.mock(/\/api\/blade-system\/business-param\/metadata\/update/, 'post', fakeSuccess);
  Mock.mock(/\/api\/blade-system\/business-param\/metadata\/remove/, 'post', fakeSuccess);
  
  Mock.mock(/\/api\/blade-system\/business-param\/value\/list/, 'get', (options) => {
    const params = new URLSearchParams(options.url.split('?')[1])
    return getFakeList({
      current: params.get('current') || 1,
      size: params.get('size') || 10,
      type: 'value'
    })
  });
  
  Mock.mock(/\/api\/blade-system\/business-param\/value\/submit/, 'post', fakeSuccess);
  Mock.mock(/\/api\/blade-system\/business-param\/value\/update/, 'post', fakeSuccess);
  Mock.mock(/\/api\/blade-system\/business-param\/value\/remove/, 'post', fakeSuccess);
} 