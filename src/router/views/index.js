import Layout from '@/page/index/'

export default [
  // {
  //   path: '/parkingDetail',
  //   component: () =>
  //     import( /* webpackChunkName: "page" */ '@/views/bizManage/offRoadParking/components/parkingDetail'),
  //   name: '停车详情',
  //   meta: {
  //     keepAlive: true,
  //     isTab: false,
  //     isAuth: false
  //   }
  // },

  {
    path: '/parkingDetail',
    component: Layout,
    redirect: '/parkingDetail/index',
    children: [{
      path: 'index',
      name: '停车详情',
      meta: {
        i18n: 'parkingDetail'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/bizManage/offRoadParking/components/parkingDetail')
    }]
  },

  {
    path: '/test',
    component: Layout,
    redirect: '/test/index',
    children: [{
      path: 'index',
      name: '测试页',
      meta: {
        i18n: 'test'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/test')
    }]
  },
  {
    path: '/testBusinessParam',
    component: Layout,
    redirect: '/testBusinessParam/index',
    children: [{
      path: 'index',
      name: '业务参数测试',
      meta: {
        i18n: 'testBusinessParam'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/testBusinessParam')
    }]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/businessParam',
    children: [{
      path: 'businessParam',
      name: '业务参数管理',
      meta: {
        i18n: 'businessParam'
      },
      component: () =>
        import( /* webpackChunkName: "views" */ '@/views/system/businessParam')
    }]
  },
  {
  path: '/wel',
  component: Layout,
  redirect: '/wel/index',
  children: [{
    path: 'index',
    name: '首页',
    meta: {
      i18n: 'dashboard'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/welpark/index')
    //   import( /* webpackChunkName: "views" */ '@/views/wel/index')
  }, {
    path: 'dashboard',
    name: '控制台',
    meta: {
      i18n: 'dashboard',
      menu: false,
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/dashboard')
  }]
}, {
  path: '/test',
  component: Layout,
  redirect: '/test/index',
  children: [{
    path: 'index',
    name: '测试页',
    meta: {
      i18n: 'test'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/test')
  }]
}, {
  path: '/dict-horizontal',
  component: Layout,
  redirect: '/dict-horizontal/index',
  children: [{
    path: 'index',
    name: '字典管理',
    meta: {
      i18n: 'dict'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/util/demo/dict-horizontal')
  }]
}, {
  path: '/dict-vertical',
  component: Layout,
  redirect: '/dict-vertical/index',
  children: [{
    path: 'index',
    name: '字典管理',
    meta: {
      i18n: 'dict'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/util/demo/dict-vertical')
  }]
}, {
  path: '/info',
  component: Layout,
  redirect: '/info/index',
  children: [{
    path: 'index',
    name: '个人信息',
    meta: {
      i18n: 'info'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/system/userinfo')
  }]
}, {
  path: '/work/process/leave',
  component: Layout,
  redirect: '/work/process/leave/form',
  children: [{
    path: 'form/:processDefinitionId',
    name: '请假流程',
    meta: {
      i18n: 'work'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/work/process/leave/form')
  }, {
    path: 'handle/:taskId/:processInstanceId/:businessId',
    name: '处理请假流程',
    meta: {
      i18n: 'work'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/work/process/leave/handle')
  }, {
    path: 'detail/:processInstanceId/:businessId',
    name: '请假流程详情',
    meta: {
      i18n: 'work'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/work/process/leave/detail')
  }]
}]
